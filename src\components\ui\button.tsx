import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-mystical-600 text-white hover:bg-mystical-700 hover:shadow-mystical active:bg-mystical-800',
        destructive: 'bg-red-600 text-white hover:bg-red-700 hover:shadow-lg active:bg-red-800',
        outline: 'border border-mystical-300 bg-transparent text-mystical-700 hover:bg-mystical-50 hover:text-mystical-800 dark:border-mystical-600 dark:text-mystical-400 dark:hover:bg-mystical-900/20',
        secondary: 'bg-mystical-100 text-mystical-800 hover:bg-mystical-200 hover:shadow-sm dark:bg-mystical-800/20 dark:text-mystical-200 dark:hover:bg-mystical-800/40',
        ghost: 'text-mystical-700 hover:bg-mystical-100 hover:text-mystical-800 dark:text-mystical-400 dark:hover:bg-mystical-900/20 dark:hover:text-mystical-300',
        link: 'text-mystical-600 underline-offset-4 hover:underline hover:text-mystical-700',
        mystical: 'bg-gradient-to-r from-mystical-500 to-mystical-600 text-white hover:from-mystical-600 hover:to-mystical-700 hover:shadow-mystical',
        golden: 'bg-gradient-to-r from-gold-500 to-gold-600 text-white hover:from-gold-600 hover:to-gold-700 hover:shadow-gold hover:scale-105 active:scale-100',
        glass: 'bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 hover:border-white/30',
      },
      size: {
        xs: 'h-8 px-3 text-xs rounded-md',
        sm: 'h-9 px-4 text-sm rounded-md',
        default: 'h-10 px-6 py-2',
        lg: 'h-11 px-8 text-base',
        xl: 'h-12 px-10 text-lg',
        icon: 'h-10 w-10 p-0',
        'icon-sm': 'h-8 w-8 p-0',
        'icon-lg': 'h-12 w-12 p-0',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loadingText?: string;
  fullWidth?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      leftIcon,
      rightIcon,
      loadingText,
      fullWidth = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';

    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size }),
          fullWidth && 'w-full',
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {!loading && leftIcon && (
          <span className={cn("flex-shrink-0", children && "mr-2")}>
            {leftIcon}
          </span>
        )}
        <span className="flex-1 truncate">
          {loading && loadingText ? loadingText : children}
        </span>
        {!loading && rightIcon && (
          <span className={cn("flex-shrink-0", children && "ml-2")}>
            {rightIcon}
          </span>
        )}
      </Comp>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
