'use client';

import Script from 'next/script';
import { useLocale } from 'next-intl';
import { generateStructuredData } from '@/lib/seo-config';
import { type Locale } from '@/i18n';

interface StructuredDataProps {
  data?: string | Record<string, any>;
  id?: string;
  type?: 'website' | 'article' | 'organization';
}

/**
 * 结构化数据组件
 * 用于在页面中插入JSON-LD结构化数据
 */
export function StructuredData({ data, id, type = 'website' }: StructuredDataProps) {
  const locale = useLocale() as Locale;

  // 如果没有提供数据，生成默认数据
  let structuredData: string;

  if (typeof data === 'string') {
    structuredData = data;
  } else if (typeof data === 'object' && data !== null) {
    structuredData = JSON.stringify(data, null, 2);
  } else {
    // 生成默认结构化数据
    const defaultData = generateStructuredData(locale, type);
    structuredData = JSON.stringify(defaultData, null, 2);
  }

  return (
    <Script
      id={id || `structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: structuredData }}
    />
  );
}

interface WebsiteStructuredDataProps {
  locale?: string;
}

/**
 * 网站结构化数据组件
 */
export function WebsiteStructuredData({ locale = 'en' }: WebsiteStructuredDataProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Mystical Website',
    description: 'Professional mystical platform offering free tarot, astrology, and numerology tests with AI analysis.',
    url: process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com',
    inLanguage: locale,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };

  return <StructuredData data={JSON.stringify(data)} id="website-structured-data" />;
}

interface ArticleStructuredDataProps {
  title: string;
  description: string;
  author: string;
  publishedTime: string;
  modifiedTime?: string;
  image?: string;
  url: string;
  category: string;
}

/**
 * 文章结构化数据组件
 */
export function ArticleStructuredData({
  title,
  description,
  author,
  publishedTime,
  modifiedTime,
  image,
  url,
  category,
}: ArticleStructuredDataProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description,
    author: {
      '@type': 'Person',
      name: author,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Mystical Website',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/images/logo.png`,
      },
    },
    datePublished: publishedTime,
    dateModified: modifiedTime || publishedTime,
    image,
    url,
    mainEntityOfPage: url,
    articleSection: category,
  };

  return <StructuredData data={JSON.stringify(data)} id="article-structured-data" />;
}

interface BreadcrumbStructuredDataProps {
  breadcrumbs: Array<{ name: string; url: string }>;
}

/**
 * 面包屑结构化数据组件
 */
export function BreadcrumbStructuredData({ breadcrumbs }: BreadcrumbStructuredDataProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return <StructuredData data={JSON.stringify(data)} id="breadcrumb-structured-data" />;
}

interface FAQStructuredDataProps {
  faqs: Array<{ question: string; answer: string }>;
}

/**
 * FAQ结构化数据组件
 */
export function FAQStructuredData({ faqs }: FAQStructuredDataProps) {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return <StructuredData data={JSON.stringify(data)} id="faq-structured-data" />;
}

/**
 * 组织结构化数据组件
 */
export function OrganizationStructuredData() {
  const data = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Mystical Website',
    description: 'Professional mystical platform offering free tarot, astrology, and numerology tests.',
    url: process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com',
    logo: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/images/logo.png`,
    sameAs: [
      'https://twitter.com/mystical_website',
      'https://facebook.com/mystical_website',
      'https://instagram.com/mystical_website',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      email: '<EMAIL>',
    },
  };

  return <StructuredData data={JSON.stringify(data)} id="organization-structured-data" />;
}
