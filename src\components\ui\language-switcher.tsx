'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';
import { ChevronDown, Globe, Check } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { locales, languageConfig, type Locale } from '@/i18n';

interface LanguageSwitcherProps {
  variant?: 'default' | 'compact' | 'mobile';
  showFlags?: boolean;
  showNativeNames?: boolean;
  className?: string;
}

export function LanguageSwitcher({
  variant = 'default',
  showFlags = true,
  showNativeNames = true,
  className,
}: LanguageSwitcherProps) {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale() as Locale;
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // 确保组件在客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 处理语言切换
  const handleLanguageChange = (newLocale: Locale) => {
    if (newLocale === currentLocale) {
      setIsOpen(false);
      return;
    }

    // 保存语言偏好到localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-locale', newLocale);
    }

    // 构建新的路径
    const segments = pathname.split('/');
    segments[1] = newLocale; // 替换语言段
    const newPath = segments.join('/');

    // 导航到新语言页面
    router.push(newPath);
    setIsOpen(false);
  };

  // 获取当前语言配置
  const currentConfig = languageConfig[currentLocale];

  // 如果还没有在客户端渲染，显示占位符
  if (!isClient) {
    return (
      <div className={cn('w-10 h-10 bg-muted rounded-md animate-pulse', className)} />
    );
  }

  // 移动端变体
  if (variant === 'mobile') {
    return (
      <div className={cn('relative', className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full justify-between"
        >
          <div className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            {showFlags && <span>{currentConfig.flag}</span>}
            <span className="text-sm">
              {showNativeNames ? currentConfig.nativeName : currentConfig.name}
            </span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform',
              isOpen && 'rotate-180'
            )}
          />
        </Button>

        {isOpen && (
          <>
            <div
              className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
              onClick={() => setIsOpen(false)}
            />
            <Card className="absolute top-full left-0 right-0 mt-2 z-50 p-2 max-h-64 overflow-y-auto">
              <div className="space-y-1">
                {locales.map((locale) => {
                  const config = languageConfig[locale];
                  const isActive = locale === currentLocale;
                  
                  return (
                    <button
                      key={locale}
                      onClick={() => handleLanguageChange(locale)}
                      className={cn(
                        'w-full flex items-center justify-between p-3 rounded-md text-left transition-colors',
                        'hover:bg-accent/50',
                        isActive && 'bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300'
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        {showFlags && (
                          <span className="text-lg">{config.flag}</span>
                        )}
                        <div>
                          <div className="font-medium">
                            {showNativeNames ? config.nativeName : config.name}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {config.region}
                          </div>
                        </div>
                      </div>
                      {isActive && (
                        <Check className="h-4 w-4 text-mystical-600" />
                      )}
                    </button>
                  );
                })}
              </div>
            </Card>
          </>
        )}
      </div>
    );
  }

  // 紧凑变体
  if (variant === 'compact') {
    return (
      <div className={cn('relative', className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsOpen(!isOpen)}
          className="h-8 px-2"
        >
          {showFlags && <span className="mr-1">{currentConfig.flag}</span>}
          <span className="text-xs font-medium">
            {currentLocale.toUpperCase()}
          </span>
          <ChevronDown className="ml-1 h-3 w-3" />
        </Button>

        {isOpen && (
          <>
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            <Card className="absolute top-full right-0 mt-1 z-50 p-1 min-w-[200px]">
              <div className="space-y-0.5">
                {locales.map((locale) => {
                  const config = languageConfig[locale];
                  const isActive = locale === currentLocale;
                  
                  return (
                    <button
                      key={locale}
                      onClick={() => handleLanguageChange(locale)}
                      className={cn(
                        'w-full flex items-center space-x-2 p-2 rounded text-left text-sm transition-colors',
                        'hover:bg-accent/50',
                        isActive && 'bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20'
                      )}
                    >
                      {showFlags && <span>{config.flag}</span>}
                      <span>
                        {showNativeNames ? config.nativeName : config.name}
                      </span>
                      {isActive && <Check className="ml-auto h-3 w-3" />}
                    </button>
                  );
                })}
              </div>
            </Card>
          </>
        )}
      </div>
    );
  }

  // 默认变体（桌面端）
  return (
    <div className={cn('relative', className)}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2"
      >
        <Globe className="h-4 w-4" />
        {showFlags && <span>{currentConfig.flag}</span>}
        <span>
          {showNativeNames ? currentConfig.nativeName : currentConfig.name}
        </span>
        <ChevronDown
          className={cn(
            'h-4 w-4 transition-transform',
            isOpen && 'rotate-180'
          )}
        />
      </Button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          <Card className="absolute top-full right-0 mt-2 z-50 p-2 min-w-[280px] max-h-80 overflow-y-auto">
            <div className="space-y-1">
              {locales.map((locale) => {
                const config = languageConfig[locale];
                const isActive = locale === currentLocale;
                
                return (
                  <button
                    key={locale}
                    onClick={() => handleLanguageChange(locale)}
                    className={cn(
                      'w-full flex items-center justify-between p-3 rounded-md text-left transition-colors',
                      'hover:bg-accent/50',
                      isActive && 'bg-mystical-50 text-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300'
                    )}
                  >
                    <div className="flex items-center space-x-3">
                      {showFlags && (
                        <span className="text-lg">{config.flag}</span>
                      )}
                      <div>
                        <div className="font-medium">
                          {showNativeNames ? config.nativeName : config.name}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {config.region} • {config.population}
                        </div>
                      </div>
                    </div>
                    {isActive && (
                      <Check className="h-4 w-4 text-mystical-600" />
                    )}
                  </button>
                );
              })}
            </div>
          </Card>
        </>
      )}
    </div>
  );
}

// 语言切换Hook - 用于程序化语言切换
export function useLanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale() as Locale;

  const switchLanguage = (newLocale: Locale) => {
    if (newLocale === currentLocale) return;

    // 保存语言偏好
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-locale', newLocale);
    }

    // 构建新路径
    const segments = pathname.split('/');
    segments[1] = newLocale;
    const newPath = segments.join('/');

    router.push(newPath);
  };

  return {
    currentLocale,
    switchLanguage,
    availableLocales: locales,
    languageConfig,
  };
}
