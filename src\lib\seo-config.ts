import type { Metadata } from 'next';
import { locales, languageConfig, type Locale } from '@/i18n';

// SEO配置类型
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  openGraph: {
    title: string;
    description: string;
    siteName: string;
    locale: string;
    type: 'website' | 'article';
  };
  twitter: {
    title: string;
    description: string;
    card: 'summary' | 'summary_large_image';
  };
  alternates?: {
    canonical: string;
    languages: Record<string, string>;
  };
  robots?: {
    index: boolean;
    follow: boolean;
  };
}

// 基础网站信息
const baseUrl = process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com';
const siteName = 'Mystical Website';

// 多语言SEO配置
export const seoConfigs: Record<Locale, SEOConfig> = {
  en: {
    title: 'Mystical Website - Free Tarot, Astrology & Numerology Tests',
    description: 'Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.',
    keywords: [
      'mystical tests', 'free tests', 'AI analysis', 'tarot reading', 'astrology', 
      'numerology', 'personality test', 'spiritual guidance', 'fortune telling',
      'horoscope', 'zodiac signs', 'tarot cards', 'crystal healing', 'palmistry'
    ],
    openGraph: {
      title: 'Mystical Website - Free Tarot, Astrology & Numerology Tests',
      description: 'Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.',
      siteName,
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      title: 'Mystical Website - Free Tarot, Astrology & Numerology Tests',
      description: 'Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.',
      card: 'summary_large_image',
    },
    robots: {
      index: true,
      follow: true,
    },
  },
  
  zh: {
    title: '神秘网站 - 免费塔罗牌、占星术和数字命理测试',
    description: '专业的在线神秘学平台，提供免费的塔罗牌、占星术和数字命理测试，配备AI智能分析。准确的性格洞察和人生指导。',
    keywords: [
      '神秘学测试', '免费测试', 'AI分析', '塔罗牌占卜', '占星术', 
      '数字命理', '性格测试', '精神指导', '算命', '星座运势',
      '十二星座', '塔罗牌', '水晶疗愈', '手相学', '梦境解析'
    ],
    openGraph: {
      title: '神秘网站 - 免费塔罗牌、占星术和数字命理测试',
      description: '专业的在线神秘学平台，提供免费的塔罗牌、占星术和数字命理测试，配备AI智能分析。',
      siteName: '神秘网站',
      locale: 'zh_CN',
      type: 'website',
    },
    twitter: {
      title: '神秘网站 - 免费塔罗牌、占星术和数字命理测试',
      description: '专业的在线神秘学平台，提供免费的塔罗牌、占星术和数字命理测试，配备AI智能分析。',
      card: 'summary_large_image',
    },
    robots: {
      index: true,
      follow: true,
    },
  },
  
  es: {
    title: 'Sitio Web Místico - Pruebas Gratuitas de Tarot, Astrología y Numerología',
    description: 'Plataforma mística online profesional que ofrece pruebas gratuitas de tarot, astrología y numerología con análisis impulsado por IA. Perspectivas precisas de personalidad y orientación vital.',
    keywords: [
      'pruebas místicas', 'pruebas gratuitas', 'análisis IA', 'lectura de tarot', 'astrología',
      'numerología', 'test de personalidad', 'guía espiritual', 'adivinación',
      'horóscopo', 'signos del zodíaco', 'cartas del tarot', 'sanación con cristales', 'quiromancia'
    ],
    openGraph: {
      title: 'Sitio Web Místico - Pruebas Gratuitas de Tarot, Astrología y Numerología',
      description: 'Plataforma mística online profesional que ofrece pruebas gratuitas de tarot, astrología y numerología con análisis impulsado por IA.',
      siteName: 'Sitio Web Místico',
      locale: 'es_ES',
      type: 'website',
    },
    twitter: {
      title: 'Sitio Web Místico - Pruebas Gratuitas de Tarot, Astrología y Numerología',
      description: 'Plataforma mística online profesional que ofrece pruebas gratuitas de tarot, astrología y numerología con análisis impulsado por IA.',
      card: 'summary_large_image',
    },
    robots: {
      index: true,
      follow: true,
    },
  },
  
  pt: {
    title: 'Site Místico - Testes Gratuitos de Tarô, Astrologia e Numerologia',
    description: 'Plataforma mística online profissional oferecendo testes gratuitos de tarô, astrologia e numerologia com análise alimentada por IA. Insights precisos de personalidade e orientação de vida.',
    keywords: [
      'testes místicos', 'testes gratuitos', 'análise IA', 'leitura de tarô', 'astrologia',
      'numerologia', 'teste de personalidade', 'orientação espiritual', 'adivinhação',
      'horóscopo', 'signos do zodíaco', 'cartas de tarô', 'cura com cristais', 'quiromancia'
    ],
    openGraph: {
      title: 'Site Místico - Testes Gratuitos de Tarô, Astrologia e Numerologia',
      description: 'Plataforma mística online profissional oferecendo testes gratuitos de tarô, astrologia e numerologia com análise alimentada por IA.',
      siteName: 'Site Místico',
      locale: 'pt_BR',
      type: 'website',
    },
    twitter: {
      title: 'Site Místico - Testes Gratuitos de Tarô, Astrologia e Numerologia',
      description: 'Plataforma mística online profissional oferecendo testes gratuitos de tarô, astrologia e numerologia com análise alimentada por IA.',
      card: 'summary_large_image',
    },
    robots: {
      index: true,
      follow: true,
    },
  },
  
  hi: {
    title: 'रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण',
    description: 'पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है। सटीक व्यक्तित्व अंतर्दृष्टि और जीवन मार्गदर्शन।',
    keywords: [
      'रहस्यमय परीक्षण', 'मुफ्त परीक्षण', 'AI विश्लेषण', 'टैरो रीडिंग', 'ज्योतिष',
      'अंकशास्त्र', 'व्यक्तित्व परीक्षण', 'आध्यात्मिक मार्गदर्शन', 'भविष्यवाणी',
      'राशिफल', 'राशि चक्र', 'टैरो कार्ड', 'क्रिस्टल हीलिंग', 'हस्तरेखा'
    ],
    openGraph: {
      title: 'रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण',
      description: 'पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है।',
      siteName: 'रहस्यमय वेबसाइट',
      locale: 'hi_IN',
      type: 'website',
    },
    twitter: {
      title: 'रहस्यमय वेबसाइट - मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण',
      description: 'पेशेवर ऑनलाइन रहस्यमय प्लेटफॉर्म जो AI-संचालित विश्लेषण के साथ मुफ्त टैरो, ज्योतिष और अंकशास्त्र परीक्षण प्रदान करता है।',
      card: 'summary_large_image',
    },
    robots: {
      index: true,
      follow: true,
    },
  },
  
  ja: {
    title: 'ミスティカルウェブサイト - 無料タロット、占星術、数秘術テスト',
    description: 'AI分析を搭載した無料のタロット、占星術、数秘術テストを提供するプロフェッショナルなオンライン神秘学プラットフォーム。正確な性格洞察と人生指導。',
    keywords: [
      '神秘学テスト', '無料テスト', 'AI分析', 'タロット占い', '占星術',
      '数秘術', '性格テスト', 'スピリチュアルガイダンス', '占い',
      'ホロスコープ', '星座', 'タロットカード', 'クリスタルヒーリング', '手相占い'
    ],
    openGraph: {
      title: 'ミスティカルウェブサイト - 無料タロット、占星術、数秘術テスト',
      description: 'AI分析を搭載した無料のタロット、占星術、数秘術テストを提供するプロフェッショナルなオンライン神秘学プラットフォーム。',
      siteName: 'ミスティカルウェブサイト',
      locale: 'ja_JP',
      type: 'website',
    },
    twitter: {
      title: 'ミスティカルウェブサイト - 無料タロット、占星術、数秘術テスト',
      description: 'AI分析を搭載した無料のタロット、占星術、数秘術テストを提供するプロフェッショナルなオンライン神秘学プラットフォーム。',
      card: 'summary_large_image',
    },
    robots: {
      index: true,
      follow: true,
    },
  },
};

// 生成多语言元数据
export function generateLocalizedMetadata(
  locale: Locale,
  path: string = '',
  overrides: Partial<SEOConfig> = {}
): Metadata {
  const config = seoConfigs[locale];
  const mergedConfig = { ...config, ...overrides };
  
  const url = `${baseUrl}/${locale}${path}`;
  
  // 生成hreflang链接
  const languages = Object.fromEntries(
    locales.map(loc => [
      languageConfig[loc].hreflang,
      `${baseUrl}/${loc}${path}`
    ])
  );
  
  return {
    title: mergedConfig.title,
    description: mergedConfig.description,
    keywords: mergedConfig.keywords.join(', '),
    authors: [{ name: 'Mystical Website Team' }],
    creator: 'Mystical Website',
    publisher: 'Mystical Website',
    robots: {
      index: mergedConfig.robots?.index ?? true,
      follow: mergedConfig.robots?.follow ?? true,
      googleBot: {
        index: mergedConfig.robots?.index ?? true,
        follow: mergedConfig.robots?.follow ?? true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: mergedConfig.openGraph.type,
      locale: mergedConfig.openGraph.locale,
      url,
      siteName: mergedConfig.openGraph.siteName,
      title: mergedConfig.openGraph.title,
      description: mergedConfig.openGraph.description,
      images: [
        {
          url: `${baseUrl}/images/og-image-${locale}.jpg`,
          width: 1200,
          height: 630,
          alt: mergedConfig.openGraph.title,
        },
      ],
    },
    twitter: {
      card: mergedConfig.twitter.card,
      title: mergedConfig.twitter.title,
      description: mergedConfig.twitter.description,
      creator: '@mystical_website',
      images: [`${baseUrl}/images/twitter-image-${locale}.jpg`],
    },
    alternates: {
      canonical: url,
      languages,
    },
    verification: {
      google: process.env['NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION'] || undefined,
      other: {
        'msvalidate.01': process.env['NEXT_PUBLIC_BING_SITE_VERIFICATION'] || '',
      },
    },
    other: {
      'content-language': languageConfig[locale].locale,
    },
  };
}

// 生成结构化数据
export function generateStructuredData(locale: Locale, type: 'website' | 'article' | 'organization' = 'website') {
  const config = seoConfigs[locale];
  const url = `${baseUrl}/${locale}`;
  
  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': type === 'website' ? 'WebSite' : type === 'article' ? 'Article' : 'Organization',
    name: config.openGraph.siteName,
    url,
    description: config.description,
    inLanguage: languageConfig[locale].hreflang,
  };
  
  if (type === 'website') {
    return {
      ...baseStructuredData,
      '@type': 'WebSite',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${url}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string',
      },
      publisher: {
        '@type': 'Organization',
        name: 'Mystical Website',
        url: baseUrl,
        logo: `${baseUrl}/images/logo.png`,
      },
    };
  }
  
  if (type === 'organization') {
    return {
      ...baseStructuredData,
      '@type': 'Organization',
      logo: `${baseUrl}/images/logo.png`,
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        availableLanguage: locales.map(loc => languageConfig[loc].name),
      },
      sameAs: [
        'https://twitter.com/mystical_website',
        'https://facebook.com/mystical_website',
        'https://instagram.com/mystical_website',
      ],
    };
  }
  
  return baseStructuredData;
}

// 页面特定的SEO配置
export const pageConfigs = {
  home: (locale: Locale) => ({
    title: seoConfigs[locale].title,
    description: seoConfigs[locale].description,
  }),
  
  tarot: (locale: Locale) => {
    const titles = {
      en: 'Free Tarot Reading - AI-Powered Tarot Card Analysis',
      zh: '免费塔罗牌占卜 - AI智能塔罗牌分析',
      es: 'Lectura de Tarot Gratuita - Análisis de Cartas del Tarot con IA',
      pt: 'Leitura de Tarô Gratuita - Análise de Cartas de Tarô com IA',
      hi: 'मुफ्त टैरो रीडिंग - AI-संचालित टैरो कार्ड विश्लेषण',
      ja: '無料タロット占い - AI搭載タロットカード分析',
    };
    
    const descriptions = {
      en: 'Get accurate tarot card readings with our AI-powered analysis. Discover insights about your past, present, and future through professional tarot interpretation.',
      zh: '通过我们的AI智能分析获得准确的塔罗牌占卜。通过专业的塔罗牌解读发现您过去、现在和未来的洞察。',
      es: 'Obtén lecturas precisas de cartas del tarot con nuestro análisis impulsado por IA. Descubre perspectivas sobre tu pasado, presente y futuro a través de la interpretación profesional del tarot.',
      pt: 'Obtenha leituras precisas de cartas de tarô com nossa análise alimentada por IA. Descubra insights sobre seu passado, presente e futuro através da interpretação profissional do tarô.',
      hi: 'हमारे AI-संचालित विश्लेषण के साथ सटीक टैरो कार्ड रीडिंग प्राप्त करें। पेशेवर टैरो व्याख्या के माध्यम से अपने अतीत, वर्तमान और भविष्य के बारे में अंतर्दृष्टि खोजें।',
      ja: 'AI搭載の分析で正確なタロットカード占いを受けましょう。プロのタロット解釈を通じて、あなたの過去、現在、未来についての洞察を発見してください。',
    };
    
    return {
      title: titles[locale],
      description: descriptions[locale],
    };
  },
  
  astrology: (locale: Locale) => {
    const titles = {
      en: 'Free Astrology Reading - Zodiac Signs & Horoscope Analysis',
      zh: '免费占星术分析 - 星座和星座运势分析',
      es: 'Lectura de Astrología Gratuita - Análisis de Signos del Zodíaco y Horóscopo',
      pt: 'Leitura de Astrologia Gratuita - Análise de Signos do Zodíaco e Horóscopo',
      hi: 'मुफ्त ज्योतिष रीडिंग - राशि चक्र और राशिफल विश्लेषण',
      ja: '無料占星術鑑定 - 星座とホロスコープ分析',
    };
    
    const descriptions = {
      en: 'Explore your astrological profile with detailed zodiac sign analysis and personalized horoscope readings. Understand your personality traits and cosmic influences.',
      zh: '通过详细的星座分析和个性化星座运势阅读探索您的占星档案。了解您的性格特征和宇宙影响。',
      es: 'Explora tu perfil astrológico con análisis detallado de signos del zodíaco y lecturas de horóscopo personalizadas. Comprende tus rasgos de personalidad e influencias cósmicas.',
      pt: 'Explore seu perfil astrológico com análise detalhada de signos do zodíaco e leituras de horóscopo personalizadas. Compreenda seus traços de personalidade e influências cósmicas.',
      hi: 'विस्तृत राशि चक्र विश्लेषण और व्यक्तिगत राशिफल रीडिंग के साथ अपनी ज्योतिषीय प्रोफ़ाइल का अन्वेषण करें। अपने व्यक्तित्व लक्षणों और ब्रह्मांडीय प्रभावों को समझें।',
      ja: '詳細な星座分析とパーソナライズされたホロスコープ鑑定であなたの占星術プロファイルを探求しましょう。あなたの性格特性と宇宙の影響を理解してください。',
    };
    
    return {
      title: titles[locale],
      description: descriptions[locale],
    };
  },
  
  numerology: (locale: Locale) => {
    const titles = {
      en: 'Free Numerology Reading - Life Path Number Calculator',
      zh: '免费数字命理分析 - 生命路径数字计算器',
      es: 'Lectura de Numerología Gratuita - Calculadora del Número del Camino de Vida',
      pt: 'Leitura de Numerologia Gratuita - Calculadora do Número do Caminho da Vida',
      hi: 'मुफ्त अंकशास्त्र रीडिंग - जीवन पथ संख्या कैलकुलेटर',
      ja: '無料数秘術鑑定 - ライフパスナンバー計算機',
    };
    
    const descriptions = {
      en: 'Discover your life path number and numerological insights. Calculate your personal numbers and understand their meanings for your life journey.',
      zh: '发现您的生命路径数字和数字命理洞察。计算您的个人数字并了解它们对您人生旅程的意义。',
      es: 'Descubre tu número del camino de vida y perspectivas numerológicas. Calcula tus números personales y comprende sus significados para tu viaje de vida.',
      pt: 'Descubra seu número do caminho da vida e insights numerológicos. Calcule seus números pessoais e compreenda seus significados para sua jornada de vida.',
      hi: 'अपनी जीवन पथ संख्या और अंकशास्त्रीय अंतर्दृष्टि खोजें। अपनी व्यक्तिगत संख्याओं की गणना करें और अपनी जीवन यात्रा के लिए उनके अर्थों को समझें।',
      ja: 'あなたのライフパスナンバーと数秘術の洞察を発見しましょう。あなたの個人的な数字を計算し、人生の旅路におけるその意味を理解してください。',
    };
    
    return {
      title: titles[locale],
      description: descriptions[locale],
    };
  },
};
