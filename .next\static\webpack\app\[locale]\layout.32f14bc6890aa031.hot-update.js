"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/components/ui/cultural-provider.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/cultural-provider.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CulturalCSSVariables: function() { return /* binding */ CulturalCSSVariables; },\n/* harmony export */   CulturalProvider: function() { return /* binding */ CulturalProvider; },\n/* harmony export */   useCultural: function() { return /* binding */ useCultural; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ CulturalProvider,useCultural,CulturalCSSVariables auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n// 默认文化配置\nconst defaultCulturalConfig = {\n    fontFamily: \"latin\",\n    lineHeight: 1.6,\n    letterSpacing: \"0.01em\",\n    expansionFactor: 1.0,\n    colorPreferences: {\n        primary: \"mystical-500\",\n        secondary: \"gold-500\",\n        accent: \"mystical-300\",\n        lucky: \"gold-400\",\n        unlucky: \"dark-500\"\n    },\n    symbols: {\n        success: \"✓\",\n        error: \"✗\",\n        warning: \"⚠\",\n        info: \"ℹ\",\n        luck: \"\\uD83C\\uDF40\",\n        spirituality: \"✨\"\n    },\n    dateFormat: \"MM/dd/yyyy\",\n    timeFormat: \"12h\",\n    numberFormat: \"en-US\",\n    currency: \"USD\",\n    behaviors: {\n        showAnimations: true,\n        preferredColorScheme: \"light\",\n        preferredIconStyle: \"minimal\",\n        preferredInteractionStyle: \"direct\"\n    }\n};\n// 文化配置映射\nconst culturalConfigs = {\n    // 英语 (默认)\n    en: {\n        fontFamily: \"latin\",\n        lineHeight: 1.6,\n        letterSpacing: \"0.01em\",\n        expansionFactor: 1.0,\n        colorPreferences: {\n            primary: \"mystical-500\",\n            secondary: \"gold-500\",\n            accent: \"mystical-300\",\n            lucky: \"gold-400\",\n            unlucky: \"dark-500\"\n        },\n        symbols: {\n            success: \"✓\",\n            error: \"✗\",\n            warning: \"⚠\",\n            info: \"ℹ\",\n            luck: \"\\uD83C\\uDF40\",\n            spirituality: \"✨\"\n        },\n        dateFormat: \"MM/dd/yyyy\",\n        timeFormat: \"12h\",\n        numberFormat: \"en-US\",\n        currency: \"USD\",\n        behaviors: {\n            showAnimations: true,\n            preferredColorScheme: \"light\",\n            preferredIconStyle: \"minimal\",\n            preferredInteractionStyle: \"direct\"\n        }\n    },\n    // 中文\n    zh: {\n        fontFamily: \"chinese\",\n        lineHeight: 1.7,\n        letterSpacing: \"0.05em\",\n        wordBreak: \"break-all\",\n        expansionFactor: 0.8,\n        colorPreferences: {\n            primary: \"mystical-600\",\n            secondary: \"gold-600\",\n            accent: \"mystical-300\",\n            lucky: \"gold-500\",\n            unlucky: \"dark-500\"\n        },\n        symbols: {\n            success: \"✓\",\n            error: \"✗\",\n            warning: \"⚠\",\n            info: \"ℹ\",\n            luck: \"\\uD83E\\uDDE7\",\n            spirituality: \"☯\"\n        },\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        numberFormat: \"zh-CN\",\n        currency: \"CNY\",\n        behaviors: {\n            showAnimations: true,\n            preferredColorScheme: \"light\",\n            preferredIconStyle: \"detailed\",\n            preferredInteractionStyle: \"indirect\"\n        }\n    },\n    // 西班牙语\n    es: {\n        fontFamily: \"latin\",\n        lineHeight: 1.6,\n        letterSpacing: \"0.01em\",\n        expansionFactor: 1.25,\n        colorPreferences: {\n            primary: \"mystical-500\",\n            secondary: \"gold-500\",\n            accent: \"mystical-300\",\n            lucky: \"gold-400\",\n            unlucky: \"dark-500\"\n        },\n        symbols: {\n            success: \"✓\",\n            error: \"✗\",\n            warning: \"⚠\",\n            info: \"ℹ\",\n            luck: \"\\uD83C\\uDF40\",\n            spirituality: \"✝\"\n        },\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        numberFormat: \"es-ES\",\n        currency: \"EUR\",\n        behaviors: {\n            showAnimations: true,\n            preferredColorScheme: \"light\",\n            preferredIconStyle: \"detailed\",\n            preferredInteractionStyle: \"direct\"\n        }\n    },\n    // 葡萄牙语\n    pt: {\n        fontFamily: \"latin\",\n        lineHeight: 1.6,\n        letterSpacing: \"0.01em\",\n        expansionFactor: 1.20,\n        colorPreferences: {\n            primary: \"mystical-500\",\n            secondary: \"gold-500\",\n            accent: \"mystical-300\",\n            lucky: \"gold-400\",\n            unlucky: \"dark-500\"\n        },\n        symbols: {\n            success: \"✓\",\n            error: \"✗\",\n            warning: \"⚠\",\n            info: \"ℹ\",\n            luck: \"\\uD83C\\uDF40\",\n            spirituality: \"✝\"\n        },\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"24h\",\n        numberFormat: \"pt-BR\",\n        currency: \"BRL\",\n        behaviors: {\n            showAnimations: true,\n            preferredColorScheme: \"light\",\n            preferredIconStyle: \"detailed\",\n            preferredInteractionStyle: \"direct\"\n        }\n    },\n    // 印地语\n    hi: {\n        fontFamily: \"hindi\",\n        lineHeight: 1.8,\n        letterSpacing: \"0.02em\",\n        expansionFactor: 1.40,\n        colorPreferences: {\n            primary: \"mystical-500\",\n            secondary: \"gold-500\",\n            accent: \"mystical-300\",\n            lucky: \"gold-400\",\n            unlucky: \"dark-500\"\n        },\n        symbols: {\n            success: \"✓\",\n            error: \"✗\",\n            warning: \"⚠\",\n            info: \"ℹ\",\n            luck: \"\\uD83E\\uDE94\",\n            spirituality: \"\\uD83D\\uDD49\"\n        },\n        dateFormat: \"dd/MM/yyyy\",\n        timeFormat: \"12h\",\n        numberFormat: \"hi-IN\",\n        currency: \"INR\",\n        behaviors: {\n            showAnimations: true,\n            preferredColorScheme: \"light\",\n            preferredIconStyle: \"symbolic\",\n            preferredInteractionStyle: \"indirect\"\n        }\n    },\n    // 日语\n    ja: {\n        fontFamily: \"japanese\",\n        lineHeight: 1.7,\n        letterSpacing: \"0.05em\",\n        expansionFactor: 1.10,\n        colorPreferences: {\n            primary: \"mystical-500\",\n            secondary: \"gold-500\",\n            accent: \"mystical-300\",\n            lucky: \"gold-400\",\n            unlucky: \"dark-500\"\n        },\n        symbols: {\n            success: \"✓\",\n            error: \"✗\",\n            warning: \"⚠\",\n            info: \"ℹ\",\n            luck: \"\\uD83C\\uDF8B\",\n            spirituality: \"⛩\"\n        },\n        dateFormat: \"yyyy年MM月dd日\",\n        timeFormat: \"24h\",\n        numberFormat: \"ja-JP\",\n        currency: \"JPY\",\n        behaviors: {\n            showAnimations: true,\n            preferredColorScheme: \"light\",\n            preferredIconStyle: \"minimal\",\n            preferredInteractionStyle: \"indirect\"\n        }\n    }\n};\n// 创建文化上下文\nconst CulturalContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\n// 文化提供者组件\nfunction CulturalProvider(param) {\n    let { children, locale: propLocale } = param;\n    _s();\n    const currentLocale = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale)();\n    const locale = propLocale || currentLocale;\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 合并默认配置和语言特定配置\n    const config = {\n        ...defaultCulturalConfig,\n        ...culturalConfigs[locale] || {}\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setIsLoaded(true);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CulturalContext.Provider, {\n        value: {\n            config,\n            locale,\n            isLoaded\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\tarot-seo\\\\src\\\\components\\\\ui\\\\cultural-provider.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n_s(CulturalProvider, \"yWyRCuQiPYB1h3INT7ziaRj8Qwk=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useLocale\n    ];\n});\n_c = CulturalProvider;\n// 使用文化上下文的Hook\nfunction useCultural() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(CulturalContext);\n    if (context === undefined) {\n        throw new Error(\"useCultural must be used within a CulturalProvider\");\n    }\n    return context;\n}\n_s1(useCultural, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// 文化敏感的CSS变量提供者\nfunction CulturalCSSVariables() {\n    _s2();\n    const { config, isLoaded } = useCultural();\n    if (!isLoaded) return null;\n    // 将配置转换为CSS变量\n    const cssVars = {\n        \"--font-family\": getFontFamilyValue(config.fontFamily),\n        \"--line-height\": config.lineHeight.toString(),\n        \"--letter-spacing\": config.letterSpacing,\n        \"--word-break\": config.wordBreak || \"normal\",\n        \"--expansion-factor\": config.expansionFactor.toString(),\n        \"--primary-color\": \"var(--\".concat(config.colorPreferences.primary, \")\"),\n        \"--secondary-color\": \"var(--\".concat(config.colorPreferences.secondary, \")\"),\n        \"--accent-color\": \"var(--\".concat(config.colorPreferences.accent, \")\"),\n        \"--lucky-color\": \"var(--\".concat(config.colorPreferences.lucky, \")\"),\n        \"--unlucky-color\": \"var(--\".concat(config.colorPreferences.unlucky, \")\"),\n        \"--symbol-success\": '\"'.concat(config.symbols.success, '\"'),\n        \"--symbol-error\": '\"'.concat(config.symbols.error, '\"'),\n        \"--symbol-warning\": '\"'.concat(config.symbols.warning, '\"'),\n        \"--symbol-info\": '\"'.concat(config.symbols.info, '\"'),\n        \"--symbol-luck\": '\"'.concat(config.symbols.luck, '\"'),\n        \"--symbol-spirituality\": '\"'.concat(config.symbols.spirituality, '\"')\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n        id: \"91d82e1bd9cb2a03\",\n        dynamic: [\n            Object.entries(cssVars).map((param)=>{\n                let [key, value] = param;\n                return \"\".concat(key, \": \").concat(value, \";\");\n            }).join(\"\\n\")\n        ],\n        children: \":root{\".concat(Object.entries(cssVars).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(key, \": \").concat(value, \";\");\n        }).join(\"\\n\"))\n    }, void 0, false, void 0, this);\n}\n_s2(CulturalCSSVariables, \"9ZR8JdxsjqAlcSdExV2fEHvf3GQ=\", false, function() {\n    return [\n        useCultural\n    ];\n});\n_c1 = CulturalCSSVariables;\n// 辅助函数：获取字体系列值\nfunction getFontFamilyValue(fontFamily) {\n    switch(fontFamily){\n        case \"chinese\":\n            return 'var(--font-noto-sans-sc), \"Noto Sans SC\", \"PingFang SC\", \"Microsoft YaHei\", \"SimHei\", sans-serif';\n        case \"japanese\":\n            return 'var(--font-noto-sans-jp), \"Noto Sans JP\", \"Hiragino Sans\", \"Yu Gothic\", \"Meiryo\", sans-serif';\n        case \"hindi\":\n            return 'var(--font-noto-sans), \"Noto Sans Devanagari\", \"Mangal\", \"Kokila\", \"Arial Unicode MS\", sans-serif';\n        case \"latin\":\n        default:\n            return 'var(--font-inter), \"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif';\n    }\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"CulturalProvider\");\n$RefreshReg$(_c1, \"CulturalCSSVariables\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2N1bHR1cmFsLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXVFO0FBQ2pDO0FBcUR0QyxTQUFTO0FBQ1QsTUFBTUssd0JBQXdDO0lBQzVDQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsZUFBZTtJQUNmQyxpQkFBaUI7SUFDakJDLGtCQUFrQjtRQUNoQkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFFBQVE7UUFDUkMsT0FBTztRQUNQQyxTQUFTO0lBQ1g7SUFDQUMsU0FBUztRQUNQQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsY0FBYztJQUNoQjtJQUNBQyxZQUFZO0lBQ1pDLFlBQVk7SUFDWkMsY0FBYztJQUNkQyxVQUFVO0lBQ1ZDLFdBQVc7UUFDVEMsZ0JBQWdCO1FBQ2hCQyxzQkFBc0I7UUFDdEJDLG9CQUFvQjtRQUNwQkMsMkJBQTJCO0lBQzdCO0FBQ0Y7QUFFQSxTQUFTO0FBQ1QsTUFBTUMsa0JBQTJEO0lBQy9ELFVBQVU7SUFDVkMsSUFBSTtRQUNGM0IsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGVBQWU7UUFDZkMsaUJBQWlCO1FBQ2pCQyxrQkFBa0I7WUFDaEJDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsU0FBUztRQUNYO1FBQ0FDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLGNBQWM7UUFDaEI7UUFDQUMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGNBQWM7UUFDZEMsVUFBVTtRQUNWQyxXQUFXO1lBQ1RDLGdCQUFnQjtZQUNoQkMsc0JBQXNCO1lBQ3RCQyxvQkFBb0I7WUFDcEJDLDJCQUEyQjtRQUM3QjtJQUNGO0lBRUEsS0FBSztJQUNMRyxJQUFJO1FBQ0Y1QixZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsZUFBZTtRQUNmMkIsV0FBVztRQUNYMUIsaUJBQWlCO1FBQ2pCQyxrQkFBa0I7WUFDaEJDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsU0FBUztRQUNYO1FBQ0FDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLGNBQWM7UUFDaEI7UUFDQUMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGNBQWM7UUFDZEMsVUFBVTtRQUNWQyxXQUFXO1lBQ1RDLGdCQUFnQjtZQUNoQkMsc0JBQXNCO1lBQ3RCQyxvQkFBb0I7WUFDcEJDLDJCQUEyQjtRQUM3QjtJQUNGO0lBRUEsT0FBTztJQUNQSyxJQUFJO1FBQ0Y5QixZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxpQkFBaUI7UUFDakJDLGtCQUFrQjtZQUNoQkMsU0FBUztZQUNUQyxXQUFXO1lBQ1hDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7UUFDQUMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsY0FBYztRQUNoQjtRQUNBQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsY0FBYztRQUNkQyxVQUFVO1FBQ1ZDLFdBQVc7WUFDVEMsZ0JBQWdCO1lBQ2hCQyxzQkFBc0I7WUFDdEJDLG9CQUFvQjtZQUNwQkMsMkJBQTJCO1FBQzdCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1BNLElBQUk7UUFDRi9CLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGlCQUFpQjtRQUNqQkMsa0JBQWtCO1lBQ2hCQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsUUFBUTtZQUNSQyxPQUFPO1lBQ1BDLFNBQVM7UUFDWDtRQUNBQyxTQUFTO1lBQ1BDLFNBQVM7WUFDVEMsT0FBTztZQUNQQyxTQUFTO1lBQ1RDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxjQUFjO1FBQ2hCO1FBQ0FDLFlBQVk7UUFDWkMsWUFBWTtRQUNaQyxjQUFjO1FBQ2RDLFVBQVU7UUFDVkMsV0FBVztZQUNUQyxnQkFBZ0I7WUFDaEJDLHNCQUFzQjtZQUN0QkMsb0JBQW9CO1lBQ3BCQywyQkFBMkI7UUFDN0I7SUFDRjtJQUVBLE1BQU07SUFDTk8sSUFBSTtRQUNGaEMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGVBQWU7UUFDZkMsaUJBQWlCO1FBQ2pCQyxrQkFBa0I7WUFDaEJDLFNBQVM7WUFDVEMsV0FBVztZQUNYQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsU0FBUztRQUNYO1FBQ0FDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxPQUFPO1lBQ1BDLFNBQVM7WUFDVEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLGNBQWM7UUFDaEI7UUFDQUMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGNBQWM7UUFDZEMsVUFBVTtRQUNWQyxXQUFXO1lBQ1RDLGdCQUFnQjtZQUNoQkMsc0JBQXNCO1lBQ3RCQyxvQkFBb0I7WUFDcEJDLDJCQUEyQjtRQUM3QjtJQUNGO0lBRUEsS0FBSztJQUNMUSxJQUFJO1FBQ0ZqQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxpQkFBaUI7UUFDakJDLGtCQUFrQjtZQUNoQkMsU0FBUztZQUNUQyxXQUFXO1lBQ1hDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7UUFDQUMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLE9BQU87WUFDUEMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsY0FBYztRQUNoQjtRQUNBQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsY0FBYztRQUNkQyxVQUFVO1FBQ1ZDLFdBQVc7WUFDVEMsZ0JBQWdCO1lBQ2hCQyxzQkFBc0I7WUFDdEJDLG9CQUFvQjtZQUNwQkMsMkJBQTJCO1FBQzdCO0lBQ0Y7QUFDRjtBQVNBLFVBQVU7QUFDVixNQUFNUyxnQ0FBa0J4QyxvREFBYUEsQ0FBa0N5QztBQVF2RSxVQUFVO0FBQ0gsU0FBU0MsaUJBQWlCLEtBQXVEO1FBQXZELEVBQUVDLFFBQVEsRUFBRUMsUUFBUUMsVUFBVSxFQUF5QixHQUF2RDs7SUFDL0IsTUFBTUMsZ0JBQWdCMUMsb0RBQVNBO0lBQy9CLE1BQU13QyxTQUFTQyxjQUFjQztJQUM3QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBRzdDLCtDQUFRQSxDQUFDO0lBRXpDLGdCQUFnQjtJQUNoQixNQUFNOEMsU0FBUztRQUNiLEdBQUc1QyxxQkFBcUI7UUFDeEIsR0FBSTJCLGVBQWUsQ0FBQ1ksT0FBTyxJQUFJLENBQUMsQ0FBQztJQUNuQztJQUVBMUMsZ0RBQVNBLENBQUM7UUFDUjhDLFlBQVk7SUFDZCxHQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ1IsZ0JBQWdCVSxRQUFRO1FBQUNDLE9BQU87WUFBRUY7WUFBUUw7WUFBUUc7UUFBUztrQkFDekRKOzs7Ozs7QUFHUDtHQXBCZ0JEOztRQUNRdEMsZ0RBQVNBOzs7S0FEakJzQztBQXNCaEIsZUFBZTtBQUNSLFNBQVNVOztJQUNkLE1BQU1DLFVBQVVwRCxpREFBVUEsQ0FBQ3VDO0lBQzNCLElBQUlhLFlBQVlaLFdBQVc7UUFDekIsTUFBTSxJQUFJYSxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtJQU5nQkQ7QUFRaEIsZ0JBQWdCO0FBQ1QsU0FBU0c7O0lBQ2QsTUFBTSxFQUFFTixNQUFNLEVBQUVGLFFBQVEsRUFBRSxHQUFHSztJQUU3QixJQUFJLENBQUNMLFVBQVUsT0FBTztJQUV0QixjQUFjO0lBQ2QsTUFBTVMsVUFBVTtRQUNkLGlCQUFpQkMsbUJBQW1CUixPQUFPM0MsVUFBVTtRQUNyRCxpQkFBaUIyQyxPQUFPMUMsVUFBVSxDQUFDbUQsUUFBUTtRQUMzQyxvQkFBb0JULE9BQU96QyxhQUFhO1FBQ3hDLGdCQUFnQnlDLE9BQU9kLFNBQVMsSUFBSTtRQUNwQyxzQkFBc0JjLE9BQU94QyxlQUFlLENBQUNpRCxRQUFRO1FBQ3JELG1CQUFtQixTQUF5QyxPQUFoQ1QsT0FBT3ZDLGdCQUFnQixDQUFDQyxPQUFPLEVBQUM7UUFDNUQscUJBQXFCLFNBQTJDLE9BQWxDc0MsT0FBT3ZDLGdCQUFnQixDQUFDRSxTQUFTLEVBQUM7UUFDaEUsa0JBQWtCLFNBQXdDLE9BQS9CcUMsT0FBT3ZDLGdCQUFnQixDQUFDRyxNQUFNLEVBQUM7UUFDMUQsaUJBQWlCLFNBQXVDLE9BQTlCb0MsT0FBT3ZDLGdCQUFnQixDQUFDSSxLQUFLLEVBQUM7UUFDeEQsbUJBQW1CLFNBQXlDLE9BQWhDbUMsT0FBT3ZDLGdCQUFnQixDQUFDSyxPQUFPLEVBQUM7UUFDNUQsb0JBQW9CLElBQTJCLE9BQXZCa0MsT0FBT2pDLE9BQU8sQ0FBQ0MsT0FBTyxFQUFDO1FBQy9DLGtCQUFrQixJQUF5QixPQUFyQmdDLE9BQU9qQyxPQUFPLENBQUNFLEtBQUssRUFBQztRQUMzQyxvQkFBb0IsSUFBMkIsT0FBdkIrQixPQUFPakMsT0FBTyxDQUFDRyxPQUFPLEVBQUM7UUFDL0MsaUJBQWlCLElBQXdCLE9BQXBCOEIsT0FBT2pDLE9BQU8sQ0FBQ0ksSUFBSSxFQUFDO1FBQ3pDLGlCQUFpQixJQUF3QixPQUFwQjZCLE9BQU9qQyxPQUFPLENBQUNLLElBQUksRUFBQztRQUN6Qyx5QkFBeUIsSUFBZ0MsT0FBNUI0QixPQUFPakMsT0FBTyxDQUFDTSxZQUFZLEVBQUM7SUFDM0Q7SUFFQTs7O1lBR1FxQyxPQUFPQyxPQUFPLENBQUNKLFNBQVNLLEdBQUcsQ0FBQztvQkFBQyxDQUFDQyxLQUFLWCxNQUFNO3VCQUFLLEdBQVdBLE9BQVJXLEtBQUksTUFBVSxPQUFOWCxPQUFNO2VBQUlZLElBQUksQ0FBQzs7a0NBQXhFSixPQUFPQyxPQUFPLENBQUNKLFNBQVNLLEdBQUcsQ0FBQztnQkFBQyxDQUFDQyxLQUFLWCxNQUFNO21CQUFLLEdBQVdBLE9BQVJXLEtBQUksTUFBVSxPQUFOWCxPQUFNO1dBQUlZLElBQUksQ0FBQzs7QUFJbEY7SUFoQ2dCUjs7UUFDZUg7OztNQURmRztBQWtDaEIsZUFBZTtBQUNmLFNBQVNFLG1CQUFtQm5ELFVBQWtCO0lBQzVDLE9BQVFBO1FBQ04sS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztRQUNMO1lBQ0UsT0FBTztJQUNYO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvdWkvY3VsdHVyYWwtcHJvdmlkZXIudHN4P2Q5NDAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlTG9jYWxlIH0gZnJvbSAnbmV4dC1pbnRsJztcblxuaW1wb3J0IHsgbGFuZ3VhZ2VDb25maWcsIHR5cGUgTG9jYWxlIH0gZnJvbSAnQC9pMThuJztcblxuLy8g5paH5YyW5pWP5oSf6YWN572u57G75Z6LXG5pbnRlcmZhY2UgQ3VsdHVyYWxDb25maWcge1xuICAvLyDlrZfkvZPphY3nva5cbiAgZm9udEZhbWlseTogc3RyaW5nO1xuICBsaW5lSGVpZ2h0OiBudW1iZXI7XG4gIGxldHRlclNwYWNpbmc6IHN0cmluZztcbiAgd29yZEJyZWFrPzogc3RyaW5nO1xuICBcbiAgLy8g5paH5a2X6ZW/5bqm6YCC6YWNXG4gIGV4cGFuc2lvbkZhY3RvcjogbnVtYmVyO1xuICBcbiAgLy8g6aKc6Imy5YGP5aW9XG4gIGNvbG9yUHJlZmVyZW5jZXM6IHtcbiAgICBwcmltYXJ5OiBzdHJpbmc7XG4gICAgc2Vjb25kYXJ5OiBzdHJpbmc7XG4gICAgYWNjZW50OiBzdHJpbmc7XG4gICAgbHVja3k6IHN0cmluZztcbiAgICB1bmx1Y2t5OiBzdHJpbmc7XG4gIH07XG4gIFxuICAvLyDnrKblj7fns7vnu59cbiAgc3ltYm9sczoge1xuICAgIHN1Y2Nlc3M6IHN0cmluZztcbiAgICBlcnJvcjogc3RyaW5nO1xuICAgIHdhcm5pbmc6IHN0cmluZztcbiAgICBpbmZvOiBzdHJpbmc7XG4gICAgbHVjazogc3RyaW5nO1xuICAgIHNwaXJpdHVhbGl0eTogc3RyaW5nO1xuICB9O1xuICBcbiAgLy8g5pel5pyf5ZKM5pe26Ze05qC85byPXG4gIGRhdGVGb3JtYXQ6IHN0cmluZztcbiAgdGltZUZvcm1hdDogJzEyaCcgfCAnMjRoJztcbiAgXG4gIC8vIOaVsOWtl+agvOW8j1xuICBudW1iZXJGb3JtYXQ6IHN0cmluZztcbiAgXG4gIC8vIOi0p+W4gVxuICBjdXJyZW5jeTogc3RyaW5nO1xuICBcbiAgLy8g5paH5YyW54m55a6a6KGM5Li6XG4gIGJlaGF2aW9yczoge1xuICAgIHNob3dBbmltYXRpb25zOiBib29sZWFuO1xuICAgIHByZWZlcnJlZENvbG9yU2NoZW1lOiAnbGlnaHQnIHwgJ2RhcmsnIHwgJ3N5c3RlbSc7XG4gICAgcHJlZmVycmVkSWNvblN0eWxlOiAnbWluaW1hbCcgfCAnZGV0YWlsZWQnIHwgJ3N5bWJvbGljJztcbiAgICBwcmVmZXJyZWRJbnRlcmFjdGlvblN0eWxlOiAnZGlyZWN0JyB8ICdpbmRpcmVjdCc7XG4gIH07XG59XG5cbi8vIOm7mOiupOaWh+WMlumFjee9rlxuY29uc3QgZGVmYXVsdEN1bHR1cmFsQ29uZmlnOiBDdWx0dXJhbENvbmZpZyA9IHtcbiAgZm9udEZhbWlseTogJ2xhdGluJyxcbiAgbGluZUhlaWdodDogMS42LFxuICBsZXR0ZXJTcGFjaW5nOiAnMC4wMWVtJyxcbiAgZXhwYW5zaW9uRmFjdG9yOiAxLjAsXG4gIGNvbG9yUHJlZmVyZW5jZXM6IHtcbiAgICBwcmltYXJ5OiAnbXlzdGljYWwtNTAwJyxcbiAgICBzZWNvbmRhcnk6ICdnb2xkLTUwMCcsXG4gICAgYWNjZW50OiAnbXlzdGljYWwtMzAwJyxcbiAgICBsdWNreTogJ2dvbGQtNDAwJyxcbiAgICB1bmx1Y2t5OiAnZGFyay01MDAnLFxuICB9LFxuICBzeW1ib2xzOiB7XG4gICAgc3VjY2VzczogJ+KckycsXG4gICAgZXJyb3I6ICfinJcnLFxuICAgIHdhcm5pbmc6ICfimqAnLFxuICAgIGluZm86ICfihLknLFxuICAgIGx1Y2s6ICfwn42AJyxcbiAgICBzcGlyaXR1YWxpdHk6ICfinKgnLFxuICB9LFxuICBkYXRlRm9ybWF0OiAnTU0vZGQveXl5eScsXG4gIHRpbWVGb3JtYXQ6ICcxMmgnLFxuICBudW1iZXJGb3JtYXQ6ICdlbi1VUycsXG4gIGN1cnJlbmN5OiAnVVNEJyxcbiAgYmVoYXZpb3JzOiB7XG4gICAgc2hvd0FuaW1hdGlvbnM6IHRydWUsXG4gICAgcHJlZmVycmVkQ29sb3JTY2hlbWU6ICdsaWdodCcsXG4gICAgcHJlZmVycmVkSWNvblN0eWxlOiAnbWluaW1hbCcsXG4gICAgcHJlZmVycmVkSW50ZXJhY3Rpb25TdHlsZTogJ2RpcmVjdCcsXG4gIH0sXG59O1xuXG4vLyDmlofljJbphY3nva7mmKDlsIRcbmNvbnN0IGN1bHR1cmFsQ29uZmlnczogUmVjb3JkPHN0cmluZywgUGFydGlhbDxDdWx0dXJhbENvbmZpZz4+ID0ge1xuICAvLyDoi7Hor60gKOm7mOiupClcbiAgZW46IHtcbiAgICBmb250RmFtaWx5OiAnbGF0aW4nLFxuICAgIGxpbmVIZWlnaHQ6IDEuNixcbiAgICBsZXR0ZXJTcGFjaW5nOiAnMC4wMWVtJyxcbiAgICBleHBhbnNpb25GYWN0b3I6IDEuMCxcbiAgICBjb2xvclByZWZlcmVuY2VzOiB7XG4gICAgICBwcmltYXJ5OiAnbXlzdGljYWwtNTAwJyxcbiAgICAgIHNlY29uZGFyeTogJ2dvbGQtNTAwJyxcbiAgICAgIGFjY2VudDogJ215c3RpY2FsLTMwMCcsXG4gICAgICBsdWNreTogJ2dvbGQtNDAwJyxcbiAgICAgIHVubHVja3k6ICdkYXJrLTUwMCcsXG4gICAgfSxcbiAgICBzeW1ib2xzOiB7XG4gICAgICBzdWNjZXNzOiAn4pyTJyxcbiAgICAgIGVycm9yOiAn4pyXJyxcbiAgICAgIHdhcm5pbmc6ICfimqAnLFxuICAgICAgaW5mbzogJ+KEuScsXG4gICAgICBsdWNrOiAn8J+NgCcsXG4gICAgICBzcGlyaXR1YWxpdHk6ICfinKgnLFxuICAgIH0sXG4gICAgZGF0ZUZvcm1hdDogJ01NL2RkL3l5eXknLFxuICAgIHRpbWVGb3JtYXQ6ICcxMmgnLFxuICAgIG51bWJlckZvcm1hdDogJ2VuLVVTJyxcbiAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgYmVoYXZpb3JzOiB7XG4gICAgICBzaG93QW5pbWF0aW9uczogdHJ1ZSxcbiAgICAgIHByZWZlcnJlZENvbG9yU2NoZW1lOiAnbGlnaHQnLFxuICAgICAgcHJlZmVycmVkSWNvblN0eWxlOiAnbWluaW1hbCcsXG4gICAgICBwcmVmZXJyZWRJbnRlcmFjdGlvblN0eWxlOiAnZGlyZWN0JyxcbiAgICB9LFxuICB9LFxuICBcbiAgLy8g5Lit5paHXG4gIHpoOiB7XG4gICAgZm9udEZhbWlseTogJ2NoaW5lc2UnLFxuICAgIGxpbmVIZWlnaHQ6IDEuNyxcbiAgICBsZXR0ZXJTcGFjaW5nOiAnMC4wNWVtJyxcbiAgICB3b3JkQnJlYWs6ICdicmVhay1hbGwnLFxuICAgIGV4cGFuc2lvbkZhY3RvcjogMC44LFxuICAgIGNvbG9yUHJlZmVyZW5jZXM6IHtcbiAgICAgIHByaW1hcnk6ICdteXN0aWNhbC02MDAnLCAvLyDmm7Tmt7HnmoTntKvoibJcbiAgICAgIHNlY29uZGFyeTogJ2dvbGQtNjAwJywgICAvLyDmm7Tmt7HnmoTph5HoibJcbiAgICAgIGFjY2VudDogJ215c3RpY2FsLTMwMCcsXG4gICAgICBsdWNreTogJ2dvbGQtNTAwJywgICAgICAgLy8g6YeR6Imy5Zyo5Lit5Zu95paH5YyW5Lit5Luj6KGo5aW96L+QXG4gICAgICB1bmx1Y2t5OiAnZGFyay01MDAnLFxuICAgIH0sXG4gICAgc3ltYm9sczoge1xuICAgICAgc3VjY2VzczogJ+KckycsXG4gICAgICBlcnJvcjogJ+KclycsXG4gICAgICB3YXJuaW5nOiAn4pqgJyxcbiAgICAgIGluZm86ICfihLknLFxuICAgICAgbHVjazogJ/Cfp6cnLCAgICAgICAgICAgICAgLy8g57qi5YyF6LGh5b6B5aW96L+QXG4gICAgICBzcGlyaXR1YWxpdHk6ICfimK8nLCAgICAgICAvLyDpmLTpmLPnrKblj7dcbiAgICB9LFxuICAgIGRhdGVGb3JtYXQ6ICd5eXl55bm0TU3mnIhkZOaXpScsXG4gICAgdGltZUZvcm1hdDogJzI0aCcsXG4gICAgbnVtYmVyRm9ybWF0OiAnemgtQ04nLFxuICAgIGN1cnJlbmN5OiAnQ05ZJyxcbiAgICBiZWhhdmlvcnM6IHtcbiAgICAgIHNob3dBbmltYXRpb25zOiB0cnVlLFxuICAgICAgcHJlZmVycmVkQ29sb3JTY2hlbWU6ICdsaWdodCcsXG4gICAgICBwcmVmZXJyZWRJY29uU3R5bGU6ICdkZXRhaWxlZCcsXG4gICAgICBwcmVmZXJyZWRJbnRlcmFjdGlvblN0eWxlOiAnaW5kaXJlY3QnLFxuICAgIH0sXG4gIH0sXG4gIFxuICAvLyDopb/nj63niZnor61cbiAgZXM6IHtcbiAgICBmb250RmFtaWx5OiAnbGF0aW4nLFxuICAgIGxpbmVIZWlnaHQ6IDEuNixcbiAgICBsZXR0ZXJTcGFjaW5nOiAnMC4wMWVtJyxcbiAgICBleHBhbnNpb25GYWN0b3I6IDEuMjUsXG4gICAgY29sb3JQcmVmZXJlbmNlczoge1xuICAgICAgcHJpbWFyeTogJ215c3RpY2FsLTUwMCcsXG4gICAgICBzZWNvbmRhcnk6ICdnb2xkLTUwMCcsXG4gICAgICBhY2NlbnQ6ICdteXN0aWNhbC0zMDAnLFxuICAgICAgbHVja3k6ICdnb2xkLTQwMCcsXG4gICAgICB1bmx1Y2t5OiAnZGFyay01MDAnLFxuICAgIH0sXG4gICAgc3ltYm9sczoge1xuICAgICAgc3VjY2VzczogJ+KckycsXG4gICAgICBlcnJvcjogJ+KclycsXG4gICAgICB3YXJuaW5nOiAn4pqgJyxcbiAgICAgIGluZm86ICfihLknLFxuICAgICAgbHVjazogJ/CfjYAnLFxuICAgICAgc3Bpcml0dWFsaXR5OiAn4pydJywgICAgICAgLy8g5Y2B5a2X5p62XG4gICAgfSxcbiAgICBkYXRlRm9ybWF0OiAnZGQvTU0veXl5eScsXG4gICAgdGltZUZvcm1hdDogJzI0aCcsXG4gICAgbnVtYmVyRm9ybWF0OiAnZXMtRVMnLFxuICAgIGN1cnJlbmN5OiAnRVVSJyxcbiAgICBiZWhhdmlvcnM6IHtcbiAgICAgIHNob3dBbmltYXRpb25zOiB0cnVlLFxuICAgICAgcHJlZmVycmVkQ29sb3JTY2hlbWU6ICdsaWdodCcsXG4gICAgICBwcmVmZXJyZWRJY29uU3R5bGU6ICdkZXRhaWxlZCcsXG4gICAgICBwcmVmZXJyZWRJbnRlcmFjdGlvblN0eWxlOiAnZGlyZWN0JyxcbiAgICB9LFxuICB9LFxuICBcbiAgLy8g6JGh6JCE54mZ6K+tXG4gIHB0OiB7XG4gICAgZm9udEZhbWlseTogJ2xhdGluJyxcbiAgICBsaW5lSGVpZ2h0OiAxLjYsXG4gICAgbGV0dGVyU3BhY2luZzogJzAuMDFlbScsXG4gICAgZXhwYW5zaW9uRmFjdG9yOiAxLjIwLFxuICAgIGNvbG9yUHJlZmVyZW5jZXM6IHtcbiAgICAgIHByaW1hcnk6ICdteXN0aWNhbC01MDAnLFxuICAgICAgc2Vjb25kYXJ5OiAnZ29sZC01MDAnLFxuICAgICAgYWNjZW50OiAnbXlzdGljYWwtMzAwJyxcbiAgICAgIGx1Y2t5OiAnZ29sZC00MDAnLFxuICAgICAgdW5sdWNreTogJ2RhcmstNTAwJyxcbiAgICB9LFxuICAgIHN5bWJvbHM6IHtcbiAgICAgIHN1Y2Nlc3M6ICfinJMnLFxuICAgICAgZXJyb3I6ICfinJcnLFxuICAgICAgd2FybmluZzogJ+KaoCcsXG4gICAgICBpbmZvOiAn4oS5JyxcbiAgICAgIGx1Y2s6ICfwn42AJyxcbiAgICAgIHNwaXJpdHVhbGl0eTogJ+KcnScsICAgICAgIC8vIOWNgeWtl+aetlxuICAgIH0sXG4gICAgZGF0ZUZvcm1hdDogJ2RkL01NL3l5eXknLFxuICAgIHRpbWVGb3JtYXQ6ICcyNGgnLFxuICAgIG51bWJlckZvcm1hdDogJ3B0LUJSJyxcbiAgICBjdXJyZW5jeTogJ0JSTCcsXG4gICAgYmVoYXZpb3JzOiB7XG4gICAgICBzaG93QW5pbWF0aW9uczogdHJ1ZSxcbiAgICAgIHByZWZlcnJlZENvbG9yU2NoZW1lOiAnbGlnaHQnLFxuICAgICAgcHJlZmVycmVkSWNvblN0eWxlOiAnZGV0YWlsZWQnLFxuICAgICAgcHJlZmVycmVkSW50ZXJhY3Rpb25TdHlsZTogJ2RpcmVjdCcsXG4gICAgfSxcbiAgfSxcbiAgXG4gIC8vIOWNsOWcsOivrVxuICBoaToge1xuICAgIGZvbnRGYW1pbHk6ICdoaW5kaScsXG4gICAgbGluZUhlaWdodDogMS44LFxuICAgIGxldHRlclNwYWNpbmc6ICcwLjAyZW0nLFxuICAgIGV4cGFuc2lvbkZhY3RvcjogMS40MCxcbiAgICBjb2xvclByZWZlcmVuY2VzOiB7XG4gICAgICBwcmltYXJ5OiAnbXlzdGljYWwtNTAwJyxcbiAgICAgIHNlY29uZGFyeTogJ2dvbGQtNTAwJyxcbiAgICAgIGFjY2VudDogJ215c3RpY2FsLTMwMCcsXG4gICAgICBsdWNreTogJ2dvbGQtNDAwJywgICAgICAgLy8g6YeR6Imy5Zyo5Y2w5bqm5paH5YyW5Lit5Lmf5b6I6YeN6KaBXG4gICAgICB1bmx1Y2t5OiAnZGFyay01MDAnLFxuICAgIH0sXG4gICAgc3ltYm9sczoge1xuICAgICAgc3VjY2VzczogJ+KckycsXG4gICAgICBlcnJvcjogJ+KclycsXG4gICAgICB3YXJuaW5nOiAn4pqgJyxcbiAgICAgIGluZm86ICfihLknLFxuICAgICAgbHVjazogJ/CfqpQnLCAgICAgICAgICAgICAgLy8g5o6S54Gv6IqC54GvXG4gICAgICBzcGlyaXR1YWxpdHk6ICfwn5WJJywgICAgICAgLy8g5aWl5aeG56ym5Y+3XG4gICAgfSxcbiAgICBkYXRlRm9ybWF0OiAnZGQvTU0veXl5eScsXG4gICAgdGltZUZvcm1hdDogJzEyaCcsXG4gICAgbnVtYmVyRm9ybWF0OiAnaGktSU4nLFxuICAgIGN1cnJlbmN5OiAnSU5SJyxcbiAgICBiZWhhdmlvcnM6IHtcbiAgICAgIHNob3dBbmltYXRpb25zOiB0cnVlLFxuICAgICAgcHJlZmVycmVkQ29sb3JTY2hlbWU6ICdsaWdodCcsXG4gICAgICBwcmVmZXJyZWRJY29uU3R5bGU6ICdzeW1ib2xpYycsXG4gICAgICBwcmVmZXJyZWRJbnRlcmFjdGlvblN0eWxlOiAnaW5kaXJlY3QnLFxuICAgIH0sXG4gIH0sXG4gIFxuICAvLyDml6Xor61cbiAgamE6IHtcbiAgICBmb250RmFtaWx5OiAnamFwYW5lc2UnLFxuICAgIGxpbmVIZWlnaHQ6IDEuNyxcbiAgICBsZXR0ZXJTcGFjaW5nOiAnMC4wNWVtJyxcbiAgICBleHBhbnNpb25GYWN0b3I6IDEuMTAsXG4gICAgY29sb3JQcmVmZXJlbmNlczoge1xuICAgICAgcHJpbWFyeTogJ215c3RpY2FsLTUwMCcsXG4gICAgICBzZWNvbmRhcnk6ICdnb2xkLTUwMCcsXG4gICAgICBhY2NlbnQ6ICdteXN0aWNhbC0zMDAnLFxuICAgICAgbHVja3k6ICdnb2xkLTQwMCcsXG4gICAgICB1bmx1Y2t5OiAnZGFyay01MDAnLFxuICAgIH0sXG4gICAgc3ltYm9sczoge1xuICAgICAgc3VjY2VzczogJ+KckycsXG4gICAgICBlcnJvcjogJ+KclycsXG4gICAgICB3YXJuaW5nOiAn4pqgJyxcbiAgICAgIGluZm86ICfihLknLFxuICAgICAgbHVjazogJ/CfjosnLCAgICAgICAgICAgICAgLy8g5LiD5aSV56u55a2QXG4gICAgICBzcGlyaXR1YWxpdHk6ICfim6knLCAgICAgICAvLyDnpZ7npL5cbiAgICB9LFxuICAgIGRhdGVGb3JtYXQ6ICd5eXl55bm0TU3mnIhkZOaXpScsXG4gICAgdGltZUZvcm1hdDogJzI0aCcsXG4gICAgbnVtYmVyRm9ybWF0OiAnamEtSlAnLFxuICAgIGN1cnJlbmN5OiAnSlBZJyxcbiAgICBiZWhhdmlvcnM6IHtcbiAgICAgIHNob3dBbmltYXRpb25zOiB0cnVlLFxuICAgICAgcHJlZmVycmVkQ29sb3JTY2hlbWU6ICdsaWdodCcsXG4gICAgICBwcmVmZXJyZWRJY29uU3R5bGU6ICdtaW5pbWFsJyxcbiAgICAgIHByZWZlcnJlZEludGVyYWN0aW9uU3R5bGU6ICdpbmRpcmVjdCcsXG4gICAgfSxcbiAgfSxcbn07XG5cbi8vIOaWh+WMluS4iuS4i+aWh+exu+Wei1xuaW50ZXJmYWNlIEN1bHR1cmFsQ29udGV4dFR5cGUge1xuICBjb25maWc6IEN1bHR1cmFsQ29uZmlnO1xuICBsb2NhbGU6IExvY2FsZTtcbiAgaXNMb2FkZWQ6IGJvb2xlYW47XG59XG5cbi8vIOWIm+W7uuaWh+WMluS4iuS4i+aWh1xuY29uc3QgQ3VsdHVyYWxDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxDdWx0dXJhbENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG4vLyDmlofljJbmj5DkvpvogIXlsZ7mgKdcbmludGVyZmFjZSBDdWx0dXJhbFByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xuICBsb2NhbGU/OiBMb2NhbGU7XG59XG5cbi8vIOaWh+WMluaPkOS+m+iAhee7hOS7tlxuZXhwb3J0IGZ1bmN0aW9uIEN1bHR1cmFsUHJvdmlkZXIoeyBjaGlsZHJlbiwgbG9jYWxlOiBwcm9wTG9jYWxlIH06IEN1bHR1cmFsUHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBjdXJyZW50TG9jYWxlID0gdXNlTG9jYWxlKCkgYXMgTG9jYWxlO1xuICBjb25zdCBsb2NhbGUgPSBwcm9wTG9jYWxlIHx8IGN1cnJlbnRMb2NhbGU7XG4gIGNvbnN0IFtpc0xvYWRlZCwgc2V0SXNMb2FkZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBcbiAgLy8g5ZCI5bm26buY6K6k6YWN572u5ZKM6K+t6KiA54m55a6a6YWN572uXG4gIGNvbnN0IGNvbmZpZyA9IHtcbiAgICAuLi5kZWZhdWx0Q3VsdHVyYWxDb25maWcsXG4gICAgLi4uKGN1bHR1cmFsQ29uZmlnc1tsb2NhbGVdIHx8IHt9KSxcbiAgfSBhcyBDdWx0dXJhbENvbmZpZztcbiAgXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SXNMb2FkZWQodHJ1ZSk7XG4gIH0sIFtdKTtcbiAgXG4gIHJldHVybiAoXG4gICAgPEN1bHR1cmFsQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17eyBjb25maWcsIGxvY2FsZSwgaXNMb2FkZWQgfX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9DdWx0dXJhbENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59XG5cbi8vIOS9v+eUqOaWh+WMluS4iuS4i+aWh+eahEhvb2tcbmV4cG9ydCBmdW5jdGlvbiB1c2VDdWx0dXJhbCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQ3VsdHVyYWxDb250ZXh0KTtcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQ3VsdHVyYWwgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIEN1bHR1cmFsUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cblxuLy8g5paH5YyW5pWP5oSf55qEQ1NT5Y+Y6YeP5o+Q5L6b6ICFXG5leHBvcnQgZnVuY3Rpb24gQ3VsdHVyYWxDU1NWYXJpYWJsZXMoKSB7XG4gIGNvbnN0IHsgY29uZmlnLCBpc0xvYWRlZCB9ID0gdXNlQ3VsdHVyYWwoKTtcblxuICBpZiAoIWlzTG9hZGVkKSByZXR1cm4gbnVsbDtcbiAgXG4gIC8vIOWwhumFjee9rui9rOaNouS4ukNTU+WPmOmHj1xuICBjb25zdCBjc3NWYXJzID0ge1xuICAgICctLWZvbnQtZmFtaWx5JzogZ2V0Rm9udEZhbWlseVZhbHVlKGNvbmZpZy5mb250RmFtaWx5KSxcbiAgICAnLS1saW5lLWhlaWdodCc6IGNvbmZpZy5saW5lSGVpZ2h0LnRvU3RyaW5nKCksXG4gICAgJy0tbGV0dGVyLXNwYWNpbmcnOiBjb25maWcubGV0dGVyU3BhY2luZyxcbiAgICAnLS13b3JkLWJyZWFrJzogY29uZmlnLndvcmRCcmVhayB8fCAnbm9ybWFsJyxcbiAgICAnLS1leHBhbnNpb24tZmFjdG9yJzogY29uZmlnLmV4cGFuc2lvbkZhY3Rvci50b1N0cmluZygpLFxuICAgICctLXByaW1hcnktY29sb3InOiBgdmFyKC0tJHtjb25maWcuY29sb3JQcmVmZXJlbmNlcy5wcmltYXJ5fSlgLFxuICAgICctLXNlY29uZGFyeS1jb2xvcic6IGB2YXIoLS0ke2NvbmZpZy5jb2xvclByZWZlcmVuY2VzLnNlY29uZGFyeX0pYCxcbiAgICAnLS1hY2NlbnQtY29sb3InOiBgdmFyKC0tJHtjb25maWcuY29sb3JQcmVmZXJlbmNlcy5hY2NlbnR9KWAsXG4gICAgJy0tbHVja3ktY29sb3InOiBgdmFyKC0tJHtjb25maWcuY29sb3JQcmVmZXJlbmNlcy5sdWNreX0pYCxcbiAgICAnLS11bmx1Y2t5LWNvbG9yJzogYHZhcigtLSR7Y29uZmlnLmNvbG9yUHJlZmVyZW5jZXMudW5sdWNreX0pYCxcbiAgICAnLS1zeW1ib2wtc3VjY2Vzcyc6IGBcIiR7Y29uZmlnLnN5bWJvbHMuc3VjY2Vzc31cImAsXG4gICAgJy0tc3ltYm9sLWVycm9yJzogYFwiJHtjb25maWcuc3ltYm9scy5lcnJvcn1cImAsXG4gICAgJy0tc3ltYm9sLXdhcm5pbmcnOiBgXCIke2NvbmZpZy5zeW1ib2xzLndhcm5pbmd9XCJgLFxuICAgICctLXN5bWJvbC1pbmZvJzogYFwiJHtjb25maWcuc3ltYm9scy5pbmZvfVwiYCxcbiAgICAnLS1zeW1ib2wtbHVjayc6IGBcIiR7Y29uZmlnLnN5bWJvbHMubHVja31cImAsXG4gICAgJy0tc3ltYm9sLXNwaXJpdHVhbGl0eSc6IGBcIiR7Y29uZmlnLnN5bWJvbHMuc3Bpcml0dWFsaXR5fVwiYCxcbiAgfTtcbiAgXG4gIHJldHVybiAoXG4gICAgPHN0eWxlIGpzeCBnbG9iYWw+e2BcbiAgICAgIDpyb290IHtcbiAgICAgICAgJHtPYmplY3QuZW50cmllcyhjc3NWYXJzKS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gYCR7a2V5fTogJHt2YWx1ZX07YCkuam9pbignXFxuJyl9XG4gICAgICB9XG4gICAgYH08L3N0eWxlPlxuICApO1xufVxuXG4vLyDovoXliqnlh73mlbDvvJrojrflj5blrZfkvZPns7vliJflgLxcbmZ1bmN0aW9uIGdldEZvbnRGYW1pbHlWYWx1ZShmb250RmFtaWx5OiBzdHJpbmcpOiBzdHJpbmcge1xuICBzd2l0Y2ggKGZvbnRGYW1pbHkpIHtcbiAgICBjYXNlICdjaGluZXNlJzpcbiAgICAgIHJldHVybiAndmFyKC0tZm9udC1ub3RvLXNhbnMtc2MpLCBcIk5vdG8gU2FucyBTQ1wiLCBcIlBpbmdGYW5nIFNDXCIsIFwiTWljcm9zb2Z0IFlhSGVpXCIsIFwiU2ltSGVpXCIsIHNhbnMtc2VyaWYnO1xuICAgIGNhc2UgJ2phcGFuZXNlJzpcbiAgICAgIHJldHVybiAndmFyKC0tZm9udC1ub3RvLXNhbnMtanApLCBcIk5vdG8gU2FucyBKUFwiLCBcIkhpcmFnaW5vIFNhbnNcIiwgXCJZdSBHb3RoaWNcIiwgXCJNZWlyeW9cIiwgc2Fucy1zZXJpZic7XG4gICAgY2FzZSAnaGluZGknOlxuICAgICAgcmV0dXJuICd2YXIoLS1mb250LW5vdG8tc2FucyksIFwiTm90byBTYW5zIERldmFuYWdhcmlcIiwgXCJNYW5nYWxcIiwgXCJLb2tpbGFcIiwgXCJBcmlhbCBVbmljb2RlIE1TXCIsIHNhbnMtc2VyaWYnO1xuICAgIGNhc2UgJ2xhdGluJzpcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuICd2YXIoLS1mb250LWludGVyKSwgXCJJbnRlclwiLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsIFwiU2Vnb2UgVUlcIiwgUm9ib3RvLCBzYW5zLXNlcmlmJztcbiAgfVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VMb2NhbGUiLCJkZWZhdWx0Q3VsdHVyYWxDb25maWciLCJmb250RmFtaWx5IiwibGluZUhlaWdodCIsImxldHRlclNwYWNpbmciLCJleHBhbnNpb25GYWN0b3IiLCJjb2xvclByZWZlcmVuY2VzIiwicHJpbWFyeSIsInNlY29uZGFyeSIsImFjY2VudCIsImx1Y2t5IiwidW5sdWNreSIsInN5bWJvbHMiLCJzdWNjZXNzIiwiZXJyb3IiLCJ3YXJuaW5nIiwiaW5mbyIsImx1Y2siLCJzcGlyaXR1YWxpdHkiLCJkYXRlRm9ybWF0IiwidGltZUZvcm1hdCIsIm51bWJlckZvcm1hdCIsImN1cnJlbmN5IiwiYmVoYXZpb3JzIiwic2hvd0FuaW1hdGlvbnMiLCJwcmVmZXJyZWRDb2xvclNjaGVtZSIsInByZWZlcnJlZEljb25TdHlsZSIsInByZWZlcnJlZEludGVyYWN0aW9uU3R5bGUiLCJjdWx0dXJhbENvbmZpZ3MiLCJlbiIsInpoIiwid29yZEJyZWFrIiwiZXMiLCJwdCIsImhpIiwiamEiLCJDdWx0dXJhbENvbnRleHQiLCJ1bmRlZmluZWQiLCJDdWx0dXJhbFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJsb2NhbGUiLCJwcm9wTG9jYWxlIiwiY3VycmVudExvY2FsZSIsImlzTG9hZGVkIiwic2V0SXNMb2FkZWQiLCJjb25maWciLCJQcm92aWRlciIsInZhbHVlIiwidXNlQ3VsdHVyYWwiLCJjb250ZXh0IiwiRXJyb3IiLCJDdWx0dXJhbENTU1ZhcmlhYmxlcyIsImNzc1ZhcnMiLCJnZXRGb250RmFtaWx5VmFsdWUiLCJ0b1N0cmluZyIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJrZXkiLCJqb2luIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/cultural-provider.tsx\n"));

/***/ })

});