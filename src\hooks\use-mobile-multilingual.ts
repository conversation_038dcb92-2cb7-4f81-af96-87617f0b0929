'use client';

import { useState, useEffect, useCallback } from 'react';
import { useLocale } from 'next-intl';

import { languageConfig, type Locale } from '@/i18n';
import { useCultural } from '@/components/ui/cultural-provider';
import { useRTL } from '@/components/ui/rtl-provider';

// 移动端检测
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    const checkMobile = () => {
      const userAgent = navigator.userAgent;
      const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
      const isMobileDevice = mobileRegex.test(userAgent);
      const isSmallScreen = window.innerWidth <= 768;
      
      setIsMobile(isMobileDevice || isSmallScreen);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return { isMobile, isClient };
}

// 触摸交互优化
export function useTouchOptimization() {
  const { isMobile } = useIsMobile();
  const { config } = useCultural();
  const { isRTL } = useRTL();

  // 触摸目标尺寸计算
  const getTouchTargetSize = useCallback((baseSize: number) => {
    if (!isMobile) return baseSize;
    
    // 根据语言扩展因子调整触摸目标大小
    const adjustedSize = baseSize * config.expansionFactor;
    
    // 确保最小触摸目标为44px
    return Math.max(adjustedSize, 44);
  }, [isMobile, config.expansionFactor]);

  // 触摸间距计算
  const getTouchSpacing = useCallback((baseSpacing: number) => {
    if (!isMobile) return baseSpacing;
    
    // 根据语言特性调整间距
    const languageMultiplier = {
      chinese: 1.1,
      japanese: 1.1,
      hindi: 1.2,
      latin: 1.0,
    }[config.fontFamily] || 1.0;
    
    return baseSpacing * languageMultiplier;
  }, [isMobile, config.fontFamily]);

  // 滑动手势配置
  const getSwipeConfig = useCallback(() => {
    return {
      threshold: 50,
      velocity: 0.3,
      direction: isRTL ? 'rtl' : 'ltr',
      sensitivity: isMobile ? 1.2 : 1.0,
    };
  }, [isMobile, isRTL]);

  return {
    isMobile,
    getTouchTargetSize,
    getTouchSpacing,
    getSwipeConfig,
  };
}

// 移动端字体优化
export function useMobileFontOptimization() {
  const { isMobile } = useIsMobile();
  const { config } = useCultural();
  const locale = useLocale() as Locale;

  // 移动端字体大小调整
  const getMobileFontSize = useCallback((baseFontSize: number) => {
    if (!isMobile) return baseFontSize;
    
    // 移动端字体大小调整规则
    const mobileAdjustment = {
      chinese: 1.1,    // 中文在移动端需要稍大
      japanese: 1.1,   // 日文在移动端需要稍大
      hindi: 1.15,     // 印地语需要更大的字体
      latin: 1.0,      // 拉丁字母保持原样
    }[config.fontFamily] || 1.0;
    
    return baseFontSize * mobileAdjustment;
  }, [isMobile, config.fontFamily]);

  // 移动端行高调整
  const getMobileLineHeight = useCallback((baseLineHeight: number) => {
    if (!isMobile) return baseLineHeight;
    
    // 移动端行高调整，提高可读性
    return Math.max(baseLineHeight, 1.5);
  }, [isMobile]);

  // 移动端字间距调整
  const getMobileLetterSpacing = useCallback((baseLetterSpacing: string) => {
    if (!isMobile) return baseLetterSpacing;
    
    // 移动端字间距调整
    const numericValue = parseFloat(baseLetterSpacing);
    if (isNaN(numericValue)) return baseLetterSpacing;
    
    const mobileAdjustment = {
      chinese: 1.2,
      japanese: 1.2,
      hindi: 1.1,
      latin: 1.0,
    }[config.fontFamily] || 1.0;
    
    return `${numericValue * mobileAdjustment}em`;
  }, [isMobile, config.fontFamily]);

  return {
    getMobileFontSize,
    getMobileLineHeight,
    getMobileLetterSpacing,
  };
}

// 移动端性能优化
export function useMobilePerformance() {
  const { isMobile } = useIsMobile();
  const [isLowEndDevice, setIsLowEndDevice] = useState(false);

  useEffect(() => {
    if (!isMobile) return;

    // 检测设备性能
    const checkDevicePerformance = () => {
      // 检测内存
      const memory = (navigator as any).deviceMemory;
      const isLowMemory = memory && memory <= 2;

      // 检测CPU核心数
      const cores = navigator.hardwareConcurrency;
      const isLowCPU = cores && cores <= 2;

      // 检测连接速度
      const connection = (navigator as any).connection;
      const isSlowConnection = connection && 
        (connection.effectiveType === 'slow-2g' || 
         connection.effectiveType === '2g' ||
         connection.effectiveType === '3g');

      setIsLowEndDevice(isLowMemory || isLowCPU || isSlowConnection);
    };

    checkDevicePerformance();
  }, [isMobile]);

  // 获取性能优化配置
  const getPerformanceConfig = useCallback(() => {
    return {
      enableAnimations: !isLowEndDevice,
      imageQuality: isLowEndDevice ? 70 : 85,
      lazyLoadThreshold: isLowEndDevice ? '200px' : '100px',
      prefetchCount: isLowEndDevice ? 1 : 3,
      enableVirtualization: isLowEndDevice,
    };
  }, [isLowEndDevice]);

  return {
    isMobile,
    isLowEndDevice,
    getPerformanceConfig,
  };
}

// 移动端可访问性优化
export function useMobileAccessibility() {
  const { isMobile } = useIsMobile();
  const { config } = useCultural();
  const { isRTL } = useRTL();
  const [isHighContrast, setIsHighContrast] = useState(false);
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // 检测用户偏好
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    setIsHighContrast(highContrastQuery.matches);
    setIsReducedMotion(reducedMotionQuery.matches);

    const handleHighContrastChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches);
    };

    const handleReducedMotionChange = (e: MediaQueryListEvent) => {
      setIsReducedMotion(e.matches);
    };

    highContrastQuery.addEventListener('change', handleHighContrastChange);
    reducedMotionQuery.addEventListener('change', handleReducedMotionChange);

    return () => {
      highContrastQuery.removeEventListener('change', handleHighContrastChange);
      reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
    };
  }, []);

  // 获取可访问性配置
  const getAccessibilityConfig = useCallback(() => {
    return {
      // 高对比度模式
      highContrast: isHighContrast,
      
      // 减少动画
      reduceMotion: isReducedMotion,
      
      // 触摸目标大小
      minTouchTarget: isMobile ? 44 : 32,
      
      // 焦点指示器
      focusRingSize: isMobile ? 3 : 2,
      
      // 文字缩放支持
      supportTextZoom: true,
      
      // 屏幕阅读器优化
      screenReaderOptimized: true,
      
      // 语言特定的可访问性
      languageSpecific: {
        announceDirection: isRTL,
        announceLanguage: config.locale,
        supportComplexText: ['chinese', 'japanese', 'hindi'].includes(config.fontFamily),
      },
    };
  }, [isMobile, isHighContrast, isReducedMotion, isRTL, config]);

  return {
    isHighContrast,
    isReducedMotion,
    getAccessibilityConfig,
  };
}

// 移动端输入优化
export function useMobileInput() {
  const { isMobile } = useIsMobile();
  const { config } = useCultural();
  const locale = useLocale() as Locale;

  // 获取输入法配置
  const getInputMethodConfig = useCallback(() => {
    const inputMethods = {
      chinese: {
        inputMode: 'text' as const,
        lang: 'zh',
        autoComplete: 'off',
        spellCheck: false,
      },
      japanese: {
        inputMode: 'text' as const,
        lang: 'ja',
        autoComplete: 'off',
        spellCheck: false,
      },
      hindi: {
        inputMode: 'text' as const,
        lang: 'hi',
        autoComplete: 'off',
        spellCheck: false,
      },
      latin: {
        inputMode: 'text' as const,
        lang: locale,
        autoComplete: 'on',
        spellCheck: true,
      },
    };

    return inputMethods[config.fontFamily] || inputMethods.latin;
  }, [config.fontFamily, locale]);

  // 获取虚拟键盘配置
  const getVirtualKeyboardConfig = useCallback(() => {
    if (!isMobile) return {};

    return {
      // 防止视口缩放
      preventViewportZoom: true,
      
      // 键盘显示时的布局调整
      adjustLayoutOnKeyboard: true,
      
      // 输入完成时的行为
      hideKeyboardOnSubmit: true,
      
      // 语言特定的键盘类型
      keyboardType: {
        chinese: 'default',
        japanese: 'default',
        hindi: 'default',
        latin: 'default',
      }[config.fontFamily] || 'default',
    };
  }, [isMobile, config.fontFamily]);

  return {
    getInputMethodConfig,
    getVirtualKeyboardConfig,
  };
}

// 综合移动端多语言优化Hook
export function useMobileMultilingual() {
  const touch = useTouchOptimization();
  const font = useMobileFontOptimization();
  const performance = useMobilePerformance();
  const accessibility = useMobileAccessibility();
  const input = useMobileInput();

  return {
    ...touch,
    ...font,
    ...performance,
    ...accessibility,
    ...input,
  };
}
