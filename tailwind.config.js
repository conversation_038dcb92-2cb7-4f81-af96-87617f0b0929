/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      // 神秘主题色彩系统 - 根据设计规范更新
      colors: {
        // 主色调 - 神秘紫色系
        mystical: {
          50: '#faf7ff',   // 极浅紫，背景色
          100: '#f3ecff',  // 浅紫，卡片背景
          200: '#e9d8ff',  // 淡紫，悬停状态
          300: '#d8b9ff',  // 中浅紫，边框
          400: '#c084fc',  // 中紫，次要按钮
          500: '#a855f7',  // 标准紫，主按钮
          600: '#9333ea',  // 深紫，按钮悬停
          700: '#7c3aed',  // 更深紫，激活状态
          800: '#6b21a8',  // 很深紫，文字
          900: '#581c87',  // 最深紫，标题
        },

        // 辅助色 - 黄金色系
        gold: {
          50: '#fffbeb',   // 极浅金
          100: '#fef3c7',  // 浅金
          200: '#fde68a',  // 淡金
          300: '#fcd34d',  // 中浅金
          400: '#fbbf24',  // 中金
          500: '#f59e0b',  // 标准金，强调色
          600: '#d97706',  // 深金
          700: '#b45309',  // 更深金
          800: '#92400e',  // 很深金
          900: '#78350f',  // 最深金
        },

        // 深色系 - 神秘黑色系
        dark: {
          50: '#f8fafc',   // 极浅灰
          100: '#f1f5f9',  // 浅灰
          200: '#e2e8f0',  // 淡灰
          300: '#cbd5e1',  // 中浅灰
          400: '#94a3b8',  // 中灰
          500: '#64748b',  // 标准灰
          600: '#475569',  // 深灰
          700: '#334155',  // 更深灰
          800: '#1e293b',  // 很深灰，深色模式背景
          900: '#0f172a',  // 最深灰，深色模式主背景
        },

        // 星座色彩系统
        zodiac: {
          fire: '#ff6b6b',      // 火象星座 - 红色系
          earth: '#51cf66',     // 土象星座 - 绿色系
          air: '#74c0fc',       // 风象星座 - 蓝色系
          water: '#845ef7',     // 水象星座 - 紫色系
        },

        // 背景色 - 使用CSS变量
        background: {
          DEFAULT: 'rgb(var(--background) / <alpha-value>)',
          secondary: 'rgb(var(--background-secondary) / <alpha-value>)',
          tertiary: 'rgb(var(--background-tertiary) / <alpha-value>)',
        },

        // 前景色 - 使用CSS变量
        foreground: {
          DEFAULT: 'rgb(var(--foreground) / <alpha-value>)',
          secondary: 'rgb(var(--foreground-secondary) / <alpha-value>)',
          muted: 'rgb(var(--foreground-muted) / <alpha-value>)',
        },

        // 边框色 - 使用CSS变量
        border: {
          DEFAULT: 'rgb(var(--border) / <alpha-value>)',
          secondary: 'rgb(var(--border-secondary) / <alpha-value>)',
        },

        // 卡片背景 - 使用CSS变量
        card: {
          DEFAULT: 'rgb(var(--card) / <alpha-value>)',
          secondary: 'rgb(var(--card-secondary) / <alpha-value>)',
        },

        // 状态色
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#3b82f6',
      },
      
      // 字体系统 - 多语言支持
      fontFamily: {
        // 主要字体 - 现代无衬线（支持CSS变量）
        sans: ['var(--font-inter)', 'Inter', 'Noto Sans', 'system-ui', 'sans-serif'],

        // 标题字体 - 优雅衬线
        serif: ['Playfair Display', 'Noto Serif', 'Georgia', 'serif'],

        // 神秘字体 - 装饰性
        mystical: ['Cinzel', 'Trajan Pro', 'serif'],

        // 等宽字体 - 代码/数据
        mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],

        // 多语言字体支持（使用CSS变量）
        'inter': ['var(--font-inter)', 'Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
        'noto-sans-sc': ['var(--font-noto-sans-sc)', 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', 'SimHei', 'sans-serif'],
        'noto-sans-jp': ['var(--font-noto-sans-jp)', 'Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', 'sans-serif'],
        'noto-sans': ['var(--font-noto-sans)', 'Noto Sans Devanagari', 'Mangal', 'Kokila', 'Arial Unicode MS', 'sans-serif'],
        'noto-sans-arabic': ['Noto Sans Arabic', 'Tahoma', 'Arial Unicode MS', 'sans-serif'],
        'noto-sans-kr': ['Noto Sans KR', 'Malgun Gothic', 'Apple SD Gothic Neo', 'sans-serif'],

        // 传统语言字体别名（向后兼容）
        chinese: ['var(--font-noto-sans-sc)', 'Noto Sans SC', 'PingFang SC', 'sans-serif'],
        japanese: ['var(--font-noto-sans-jp)', 'Noto Sans JP', 'sans-serif'],
        arabic: ['Noto Sans Arabic', 'sans-serif'],
        hindi: ['var(--font-noto-sans)', 'Noto Sans Devanagari', 'sans-serif'],
        korean: ['Noto Sans KR', 'sans-serif'],
      },
      
      // 字体大小
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      
      // 间距系统
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      
      // 圆角
      borderRadius: {
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem',
      },
      
      // 阴影系统 - 神秘学主题阴影
      boxShadow: {
        'mystical': '0 10px 25px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05)',
        'mystical-lg': '0 20px 40px -4px rgba(168, 85, 247, 0.15), 0 8px 16px -4px rgba(168, 85, 247, 0.1)',
        'gold': '0 10px 25px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
        'inner-mystical': 'inset 0 2px 4px 0 rgba(168, 85, 247, 0.1)',
      },
      
      // 渐变背景
      backgroundImage: {
        'mystical-gradient': 'linear-gradient(135deg, #7c3aed 0%, #a855f7 50%, #c084fc 100%)',
        'golden-gradient': 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #fcd34d 100%)',
        'night-gradient': 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
        'star-field': 'radial-gradient(circle at 25% 25%, #a855f7 0%, transparent 50%), radial-gradient(circle at 75% 75%, #f59e0b 0%, transparent 50%)',
      },
      
      // 动画 - 神秘学主题动画
      animation: {
        'mystical-glow': 'mysticalGlow 3s ease-in-out infinite alternate',
        'tarot-flip': 'tarotFlip 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
        'crystal-shine': 'crystalShine 2s ease-in-out infinite',
        'star-twinkle': 'starTwinkle 1.5s ease-in-out infinite alternate',
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'scale-in': 'scaleIn 0.3s ease-out',
        'float': 'float 3s ease-in-out infinite',
      },
      
      // 关键帧动画 - 神秘学主题关键帧
      keyframes: {
        mysticalGlow: {
          '0%': {
            boxShadow: '0 0 20px rgba(168, 85, 247, 0.3)',
            transform: 'scale(1)'
          },
          '100%': {
            boxShadow: '0 0 40px rgba(168, 85, 247, 0.6)',
            transform: 'scale(1.02)'
          }
        },
        tarotFlip: {
          '0%': { transform: 'rotateY(0deg)' },
          '50%': { transform: 'rotateY(90deg)' },
          '100%': { transform: 'rotateY(0deg)' }
        },
        crystalShine: {
          '0%, 100%': { opacity: '0.8' },
          '50%': { opacity: '1', transform: 'scale(1.05)' }
        },
        starTwinkle: {
          '0%': { opacity: '0.5', transform: 'scale(0.8)' },
          '100%': { opacity: '1', transform: 'scale(1.2)' }
        },
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' }
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' }
        }
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
    require("@tailwindcss/forms"),
    require("@tailwindcss/aspect-ratio"),
  ],
}
