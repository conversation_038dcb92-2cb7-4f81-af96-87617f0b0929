'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';
import { useCultural } from '@/components/ui/cultural-provider';
import { useRTL } from '@/components/ui/rtl-provider';
import { 
  CulturalText, 
  CulturalCardTitle, 
  SpiritualSymbol, 
  LuckySymbol 
} from '@/components/ui/cultural-symbols';

// 文化敏感卡片变体
const culturalCardVariants = cva(
  'rounded-lg border bg-card text-card-foreground shadow-sm',
  {
    variants: {
      variant: {
        default: 'bg-card',
        mystical: 'bg-mystical-50 border-mystical-200 dark:bg-mystical-900/20 dark:border-mystical-800/30',
        golden: 'bg-gold-50 border-gold-200 dark:bg-gold-900/20 dark:border-gold-800/30',
        spiritual: 'bg-gradient-to-br from-mystical-50 to-mystical-100 border-mystical-200 dark:from-mystical-900/30 dark:to-mystical-800/30 dark:border-mystical-700/30',
        lucky: 'bg-gradient-to-br from-gold-50 to-gold-100 border-gold-200 dark:from-gold-900/30 dark:to-gold-800/30 dark:border-gold-700/30',
      },
      size: {
        sm: 'p-3',
        md: 'p-4',
        lg: 'p-6',
      },
      culturalStyle: {
        default: '',
        western: '',
        eastern: '',
        indian: '',
        latinAmerican: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      culturalStyle: 'default',
    },
  }
);

// 文化敏感卡片属性
export interface CulturalCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof culturalCardVariants> {
  showSymbol?: boolean;
  symbolType?: 'spiritual' | 'lucky' | 'none';
  symbolPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  adaptiveHeight?: boolean;
}

// 文化敏感卡片组件
export const CulturalCard = React.forwardRef<HTMLDivElement, CulturalCardProps>(
  ({ 
    className, 
    variant, 
    size, 
    culturalStyle,
    showSymbol = false,
    symbolType = 'spiritual',
    symbolPosition = 'top-right',
    adaptiveHeight = true,
    children,
    ...props 
  }, ref) => {
    const { config } = useCultural();
    const { isRTL } = useRTL();
    
    // 根据文化自动选择样式
    const getAutoCulturalStyle = () => {
      if (culturalStyle !== 'default') return culturalStyle;
      
      switch (config.fontFamily) {
        case 'chinese':
        case 'japanese':
          return 'eastern';
        case 'hindi':
          return 'indian';
        case 'latin':
          if (config.locale === 'es-ES' || config.locale === 'pt-BR') {
            return 'latinAmerican';
          }
          return 'western';
        default:
          return 'western';
      }
    };
    
    const autoCulturalStyle = getAutoCulturalStyle();
    
    // 根据文化调整卡片样式
    const getCulturalClasses = () => {
      switch (autoCulturalStyle) {
        case 'eastern':
          return 'border-opacity-70 shadow-sm';
        case 'indian':
          return 'border-opacity-90 shadow-md';
        case 'latinAmerican':
          return 'border-opacity-80 shadow-md';
        case 'western':
        default:
          return 'border-opacity-100 shadow-sm';
      }
    };
    
    // 获取符号位置类
    const getSymbolPositionClass = () => {
      // 处理RTL布局
      let position = symbolPosition;
      if (isRTL) {
        if (position === 'top-left') position = 'top-right';
        else if (position === 'top-right') position = 'top-left';
        else if (position === 'bottom-left') position = 'bottom-right';
        else if (position === 'bottom-right') position = 'bottom-left';
      }
      
      switch (position) {
        case 'top-left':
          return 'top-2 left-2';
        case 'top-right':
          return 'top-2 right-2';
        case 'bottom-left':
          return 'bottom-2 left-2';
        case 'bottom-right':
          return 'bottom-2 right-2';
        default:
          return 'top-2 right-2';
      }
    };
    
    // 渲染符号
    const renderSymbol = () => {
      if (!showSymbol || symbolType === 'none') return null;
      
      return (
        <div className={`absolute ${getSymbolPositionClass()}`}>
          {symbolType === 'spiritual' ? (
            <SpiritualSymbol size="md" glowing />
          ) : (
            <LuckySymbol size="md" animated />
          )}
        </div>
      );
    };
    
    return (
      <div
        ref={ref}
        className={cn(
          culturalCardVariants({ variant, size, culturalStyle: autoCulturalStyle }),
          getCulturalClasses(),
          showSymbol && 'relative',
          adaptiveHeight && `min-h-[calc(100px*${config.expansionFactor})]`,
          className
        )}
        {...props}
      >
        {renderSymbol()}
        {children}
      </div>
    );
  }
);
CulturalCard.displayName = 'CulturalCard';

// 文化敏感卡片标题组件
interface CulturalCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  centered?: boolean;
  withSymbol?: boolean;
  symbolType?: 'spiritual' | 'lucky';
}

export const CulturalCardHeader = React.forwardRef<HTMLDivElement, CulturalCardHeaderProps>(
  ({ className, centered = false, withSymbol = false, symbolType = 'spiritual', children, ...props }, ref) => {
    const { config } = useCultural();
    
    // 根据文化调整标题样式
    const getCulturalClasses = () => {
      switch (config.fontFamily) {
        case 'chinese':
        case 'japanese':
          return 'space-y-1';
        case 'hindi':
          return 'space-y-2';
        default:
          return 'space-y-1.5';
      }
    };
    
    return (
      <div
        ref={ref}
        className={cn(
          'flex flex-col p-6',
          getCulturalClasses(),
          centered && 'items-center text-center',
          className
        )}
        {...props}
      >
        {withSymbol && (
          <div className="mb-2">
            {symbolType === 'spiritual' ? (
              <SpiritualSymbol size="lg" glowing />
            ) : (
              <LuckySymbol size="lg" animated />
            )}
          </div>
        )}
        {children}
      </div>
    );
  }
);
CulturalCardHeader.displayName = 'CulturalCardHeader';

// 文化敏感卡片标题组件
interface CulturalCardTitleComponentProps extends React.HTMLAttributes<HTMLHeadingElement> {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
}

export const CulturalCardTitleComponent = React.forwardRef<HTMLHeadingElement, CulturalCardTitleComponentProps>(
  ({ className, level = 3, children, ...props }, ref) => {
    return (
      <CulturalCardTitle
        level={level}
        className={cn('text-2xl', className)}
        {...props}
      >
        {children}
      </CulturalCardTitle>
    );
  }
);
CulturalCardTitleComponent.displayName = 'CulturalCardTitleComponent';

// 文化敏感卡片描述组件
interface CulturalCardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

export const CulturalCardDescription = React.forwardRef<HTMLParagraphElement, CulturalCardDescriptionProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <CulturalText>
        <p
          ref={ref}
          className={cn('text-sm text-muted-foreground', className)}
          {...props}
        >
          {children}
        </p>
      </CulturalText>
    );
  }
);
CulturalCardDescription.displayName = 'CulturalCardDescription';

// 文化敏感卡片内容组件
interface CulturalCardContentProps extends React.HTMLAttributes<HTMLDivElement> {}

export const CulturalCardContent = React.forwardRef<HTMLDivElement, CulturalCardContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('p-6 pt-0', className)}
        {...props}
      >
        <CulturalText>
          {children}
        </CulturalText>
      </div>
    );
  }
);
CulturalCardContent.displayName = 'CulturalCardContent';

// 文化敏感卡片底部组件
interface CulturalCardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  centered?: boolean;
}

export const CulturalCardFooter = React.forwardRef<HTMLDivElement, CulturalCardFooterProps>(
  ({ className, centered = false, children, ...props }, ref) => {
    const { config } = useCultural();
    
    return (
      <div
        ref={ref}
        className={cn(
          'flex items-center p-6 pt-0',
          centered && 'justify-center',
          config.fontFamily === 'hindi' && 'pt-2',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CulturalCardFooter.displayName = 'CulturalCardFooter';

// 导出所有组件
export {
  CulturalCard,
  CulturalCardHeader,
  CulturalCardTitleComponent as CulturalCardTitle,
  CulturalCardDescription,
  CulturalCardContent,
  CulturalCardFooter,
};
