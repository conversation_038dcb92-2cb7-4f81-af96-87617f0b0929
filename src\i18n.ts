import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// 第一阶段：核心市场语言 (立即实施)
export const locales = ['en', 'zh', 'es', 'pt', 'hi', 'ja'] as const;
export type Locale = (typeof locales)[number];

// 第二阶段：重要区域语言 (6个月后扩展)
export const phase2Locales = ['de', 'fr', 'it', 'ru', 'ko', 'ar'] as const;
export type Phase2Locale = (typeof phase2Locales)[number];

// 所有支持的语言类型
export type AllLocale = Locale | Phase2Locale;

// 语言配置 - 包含完整的多语言元数据
export const languageConfig = {
  en: {
    name: 'English',
    nativeName: 'English',
    direction: 'ltr' as const,
    region: 'Global',
    population: 'Global market',
    flag: '🇺🇸',
    hreflang: 'en',
    locale: 'en-US',
    currency: 'USD',
    fontFamily: 'latin',
    expansionFactor: 1.0,
    priority: 1,
  },
  zh: {
    name: 'Chinese',
    nativeName: '中文',
    direction: 'ltr' as const,
    region: 'China & Chinese communities',
    population: '1.4 billion',
    flag: '🇨🇳',
    hreflang: 'zh-CN',
    locale: 'zh-CN',
    currency: 'CNY',
    fontFamily: 'chinese',
    expansionFactor: 0.8,
    priority: 2,
  },
  es: {
    name: 'Spanish',
    nativeName: 'Español',
    direction: 'ltr' as const,
    region: 'Spain & Latin America',
    population: '500 million',
    flag: '🇪🇸',
    hreflang: 'es',
    locale: 'es-ES',
    currency: 'EUR',
    fontFamily: 'latin',
    expansionFactor: 1.25,
    priority: 3,
  },
  pt: {
    name: 'Portuguese',
    nativeName: 'Português',
    direction: 'ltr' as const,
    region: 'Brazil & Portuguese-speaking countries',
    population: '260 million',
    flag: '🇧🇷',
    hreflang: 'pt-BR',
    locale: 'pt-BR',
    currency: 'BRL',
    fontFamily: 'latin',
    expansionFactor: 1.20,
    priority: 4,
  },
  hi: {
    name: 'Hindi',
    nativeName: 'हिन्दी',
    direction: 'ltr' as const,
    region: 'Northern India',
    population: '600 million',
    flag: '🇮🇳',
    hreflang: 'hi-IN',
    locale: 'hi-IN',
    currency: 'INR',
    fontFamily: 'hindi',
    expansionFactor: 1.40,
    priority: 5,
  },
  ja: {
    name: 'Japanese',
    nativeName: '日本語',
    direction: 'ltr' as const,
    region: 'Japan',
    population: '125 million',
    flag: '🇯🇵',
    hreflang: 'ja-JP',
    locale: 'ja-JP',
    currency: 'JPY',
    fontFamily: 'japanese',
    expansionFactor: 1.10,
    priority: 6,
  },
} as const;

// 默认语言
export const defaultLocale: Locale = 'en';

// RTL语言配置
export const rtlLocales: readonly string[] = ['ar', 'he', 'fa', 'ur'] as const;

// 语言回退链配置
export const fallbackChain: Record<Locale, Locale[]> = {
  en: [],
  zh: ['en'],
  es: ['en'],
  pt: ['es', 'en'],
  hi: ['en'],
  ja: ['en'],
};

// 语言检测配置 - 增强版本，支持回退机制
export default getRequestConfig(async ({ locale }) => {
  // 验证传入的语言是否支持
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  try {
    // 尝试加载主要语言文件
    const messages = (await import(`../messages/${locale}.json`)).default;

    // 如果有回退语言，合并消息
    const fallbacks = fallbackChain[locale as Locale] || [];
    let mergedMessages = messages;

    for (const fallbackLocale of fallbacks) {
      try {
        const fallbackMessages = (await import(`../messages/${fallbackLocale}.json`)).default;
        mergedMessages = { ...fallbackMessages, ...mergedMessages };
      } catch (fallbackError) {
        console.warn(`Failed to load fallback messages for locale: ${fallbackLocale}`, fallbackError);
      }
    }

    return {
      messages: mergedMessages,
      timeZone: getTimeZoneForLocale(locale as Locale),
      now: new Date(),
      formats: getFormatsForLocale(locale as Locale),
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);

    // 尝试回退到默认语言
    if (locale !== defaultLocale) {
      try {
        const defaultMessages = (await import(`../messages/${defaultLocale}.json`)).default;
        return {
          messages: defaultMessages,
          timeZone: getTimeZoneForLocale(defaultLocale),
          now: new Date(),
          formats: getFormatsForLocale(defaultLocale),
        };
      } catch (defaultError) {
        console.error(`Failed to load default locale messages`, defaultError);
      }
    }

    notFound();
  }
});

// 根据语言获取时区
function getTimeZoneForLocale(locale: Locale): string {
  const timezoneMap: Record<Locale, string> = {
    en: 'America/New_York',
    zh: 'Asia/Shanghai',
    es: 'Europe/Madrid',
    pt: 'America/Sao_Paulo',
    hi: 'Asia/Kolkata',
    ja: 'Asia/Tokyo',
  };

  return timezoneMap[locale] || 'UTC';
}

// 根据语言获取格式化配置
function getFormatsForLocale(locale: Locale) {
  const formatMap: Record<Locale, any> = {
    en: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'USD'
        }
      }
    },
    zh: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'CNY'
        }
      }
    },
    es: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'EUR'
        }
      }
    },
    pt: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'BRL'
        }
      }
    },
    hi: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'INR'
        }
      }
    },
    ja: {
      dateTime: {
        short: {
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        },
        long: {
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          weekday: 'long'
        }
      },
      number: {
        currency: {
          style: 'currency',
          currency: 'JPY'
        }
      }
    }
  };

  return formatMap[locale] || formatMap[defaultLocale];
}

// 语言路径配置
export const pathnames = {
  '/': '/',
  '/blog': {
    en: '/blog',
    zh: '/blog',
    es: '/blog',
    pt: '/blog',
    hi: '/blog',
    ja: '/blog',
  },
  '/tarot': {
    en: '/tarot',
    zh: '/tarot',
    es: '/tarot',
    pt: '/tarot',
    hi: '/tarot',
    ja: '/tarot',
  },
  '/astrology': {
    en: '/astrology',
    zh: '/astrology',
    es: '/astrology',
    pt: '/astrology',
    hi: '/astrology',
    ja: '/astrology',
  },
  '/numerology': {
    en: '/numerology',
    zh: '/numerology',
    es: '/numerology',
    pt: '/numerology',
    hi: '/numerology',
    ja: '/numerology',
  },
} as const;

// 语言特定的格式化配置
export const formatConfig = {
  en: {
    dateFormat: 'MM/dd/yyyy',
    timeFormat: '12h',
    currency: 'USD',
    numberFormat: 'en-US',
  },
  zh: {
    dateFormat: 'yyyy年MM月dd日',
    timeFormat: '24h',
    currency: 'CNY',
    numberFormat: 'zh-CN',
  },
  es: {
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    currency: 'EUR',
    numberFormat: 'es-ES',
  },
  pt: {
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    currency: 'BRL',
    numberFormat: 'pt-BR',
  },
  hi: {
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '12h',
    currency: 'INR',
    numberFormat: 'hi-IN',
  },
  ja: {
    dateFormat: 'yyyy年MM月dd日',
    timeFormat: '24h',
    currency: 'JPY',
    numberFormat: 'ja-JP',
  },
} as const;
