'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Menu, X, ChevronDown } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { cn } from '@/lib/utils';

interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

export function Header() {
  const t = useTranslations('navigation');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const navigation: NavigationItem[] = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('tarot'),
      href: '/tarot',
      children: [
        { label: 'Tarot Reading', href: '/tarot/test' },
        { label: 'Tarot Cards', href: '/tarot/cards' },
        { label: 'Tarot Guide', href: '/tarot/guide' },
        { label: 'Tarot History', href: '/tarot/history' },
      ],
    },
    {
      label: t('astrology'),
      href: '/astrology',
      children: [
        { label: 'Astrology Test', href: '/astrology/test' },
        { label: 'Zodiac Signs', href: '/astrology/signs' },
        { label: 'Compatibility', href: '/astrology/compatibility' },
        { label: 'Horoscope', href: '/astrology/horoscope' },
      ],
    },
    {
      label: t('numerology'),
      href: '/numerology',
      children: [
        { label: 'Numerology Test', href: '/numerology/test' },
        { label: 'Number Calculator', href: '/numerology/calculator' },
        { label: 'Number Meanings', href: '/numerology/meanings' },
      ],
    },
    {
      label: t('blog'),
      href: '/blog',
    },
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    setActiveDropdown(null);
  };

  const toggleDropdown = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-full bg-mystical-gradient flex items-center justify-center">
              <span className="text-white font-bold text-sm">M</span>
            </div>
            <span className="font-mystical text-xl font-bold bg-mystical-gradient bg-clip-text text-transparent">
              Mystical
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <div key={item.label} className="relative group">
                {item.children ? (
                  <>
                    <button
                      className="flex items-center space-x-1 text-foreground/80 hover:text-foreground transition-colors"
                      onMouseEnter={() => setActiveDropdown(item.label)}
                    >
                      <span>{item.label}</span>
                      <ChevronDown className="h-4 w-4" />
                    </button>
                    
                    {/* Dropdown Menu */}
                    <div
                      className={cn(
                        'absolute top-full left-0 mt-2 w-48 bg-card border border-border rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200',
                        'before:absolute before:-top-2 before:left-0 before:w-full before:h-2'
                      )}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      <div className="py-2">
                        {item.children.map((child) => (
                          <Link
                            key={child.href}
                            href={child.href}
                            className="block px-4 py-2 text-sm text-foreground/80 hover:text-foreground hover:bg-accent/50 transition-colors"
                          >
                            {child.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </>
                ) : (
                  <Link
                    href={item.href}
                    className="text-foreground/80 hover:text-foreground transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Language Switcher, Theme Toggle and CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            <LanguageSwitcher variant="compact" />
            <ThemeToggle />
            <Button variant="mystical" size="sm">
              Start Free Test
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-border/40 py-4">
            <nav className="space-y-2">
              {navigation.map((item) => (
                <div key={item.label}>
                  {item.children ? (
                    <>
                      <button
                        className="flex items-center justify-between w-full px-4 py-2 text-left text-foreground/80 hover:text-foreground transition-colors"
                        onClick={() => toggleDropdown(item.label)}
                      >
                        <span>{item.label}</span>
                        <ChevronDown
                          className={cn(
                            'h-4 w-4 transition-transform',
                            activeDropdown === item.label && 'rotate-180'
                          )}
                        />
                      </button>
                      
                      {activeDropdown === item.label && (
                        <div className="pl-4 space-y-1">
                          {item.children.map((child) => (
                            <Link
                              key={child.href}
                              href={child.href}
                              className="block px-4 py-2 text-sm text-foreground/60 hover:text-foreground transition-colors"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {child.label}
                            </Link>
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <Link
                      href={item.href}
                      className="block px-4 py-2 text-foreground/80 hover:text-foreground transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  )}
                </div>
              ))}

              <div className="px-4 pt-4 space-y-3">
                <LanguageSwitcher variant="mobile" />
                <div className="flex justify-center">
                  <ThemeToggle />
                </div>
                <Button variant="mystical" size="sm" className="w-full">
                  Start Free Test
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
