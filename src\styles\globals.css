@tailwind base;
@tailwind components;
@tailwind utilities;

/*
  注意：主要字体（Inter, Noto Sans SC, Noto Sans JP, Noto Sans）
  已通过 Next.js Google Fonts 优化加载，无需在此重复导入

  以下仅导入未在 layout.tsx 中配置的装饰性字体
*/

/* 装饰性字体 - 标题和神秘元素 */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600&display=swap');

/* 第二阶段语言字体（暂时通过CSS导入，后续可迁移到Next.js字体优化） */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* 神秘紫色系变量 - 根据设计规范更新 */
    --mystical-50: 250 247 255;   /* 极浅紫，背景色 */
    --mystical-100: 243 236 255;  /* 浅紫，卡片背景 */
    --mystical-200: 233 216 255;  /* 淡紫，悬停状态 */
    --mystical-300: 216 185 255;  /* 中浅紫，边框 */
    --mystical-400: 192 132 252;  /* 中紫，次要按钮 */
    --mystical-500: 168 85 247;   /* 标准紫，主按钮 */
    --mystical-600: 147 51 234;   /* 深紫，按钮悬停 */
    --mystical-700: 124 58 237;   /* 更深紫，激活状态 */
    --mystical-800: 107 33 168;   /* 很深紫，文字 */
    --mystical-900: 88 28 135;    /* 最深紫，标题 */

    /* 黄金色系变量 */
    --gold-50: 255 251 235;   /* 极浅金 */
    --gold-100: 254 243 199;  /* 浅金 */
    --gold-200: 253 230 138;  /* 淡金 */
    --gold-300: 252 211 77;   /* 中浅金 */
    --gold-400: 251 191 36;   /* 中金 */
    --gold-500: 245 158 11;   /* 标准金，强调色 */
    --gold-600: 217 119 6;    /* 深金 */
    --gold-700: 180 83 9;     /* 更深金 */
    --gold-800: 146 64 14;    /* 很深金 */
    --gold-900: 120 53 15;    /* 最深金 */

    /* 深色系变量 */
    --dark-50: 248 250 252;   /* 极浅灰 */
    --dark-100: 241 245 249;  /* 浅灰 */
    --dark-200: 226 232 240;  /* 淡灰 */
    --dark-300: 203 213 225;  /* 中浅灰 */
    --dark-400: 148 163 184;  /* 中灰 */
    --dark-500: 100 116 139;  /* 标准灰 */
    --dark-600: 71 85 105;    /* 深灰 */
    --dark-700: 51 65 85;     /* 更深灰 */
    --dark-800: 30 41 59;     /* 很深灰，深色模式背景 */
    --dark-900: 15 23 42;     /* 最深灰，深色模式主背景 */

    /* 星座色彩系统变量 */
    --zodiac-fire: 255 107 107;    /* 火象星座 - 红色系 */
    --zodiac-earth: 81 207 102;    /* 土象星座 - 绿色系 */
    --zodiac-air: 116 192 252;     /* 风象星座 - 蓝色系 */
    --zodiac-water: 132 94 247;    /* 水象星座 - 紫色系 */

    /* 默认浅色主题变量 - 确保默认为白色背景 */
    --background: 255 255 255;
    --background-secondary: 248 250 252;
    --background-tertiary: 241 245 249;

    --foreground: 15 23 42;
    --foreground-secondary: 51 65 85;
    --foreground-muted: 100 116 139;

    --border: 226 232 240;
    --border-secondary: 203 213 225;

    --card: 255 255 255;
    --card-secondary: 248 250 252;
  }

  .dark {
    /* 深色主题变量 - 神秘深色背景 */
    --background: 15 23 42;        /* 最深灰，主背景 */
    --background-secondary: 30 41 59;    /* 很深灰，卡片背景 */
    --background-tertiary: 51 65 85;     /* 深灰，悬停背景 */

    --foreground: 255 255 255;
    --foreground-secondary: 226 232 240;
    --foreground-muted: 148 163 184;

    --border: 71 85 105;           /* 深灰边框 */
    --border-secondary: 100 116 139;     /* 中灰边框 */

    --card: 30 41 59;              /* 卡片背景 */
    --card-secondary: 51 65 85;    /* 次要卡片背景 */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 确保主题切换的平滑过渡 */
  * {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border-secondary;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-mystical-500/20 text-mystical-900;
  }

  /* 焦点样式 */
  :focus-visible {
    @apply outline-none ring-2 ring-mystical-500 ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* 神秘主题组件样式 - 根据设计规范更新 */
  .mystical-card {
    @apply bg-gradient-to-br from-card to-card/80 border border-mystical-300/20 shadow-mystical backdrop-blur-sm;
  }

  .golden-card {
    @apply bg-gradient-to-br from-card to-gold-500/5 border border-gold-500/20 shadow-gold backdrop-blur-sm;
  }

  .glass-card {
    @apply bg-white/5 border border-white/10 backdrop-blur-md shadow-lg;
  }

  /* 渐变文本 */
  .text-mystical-gradient {
    @apply bg-gradient-to-r from-mystical-400 to-mystical-600 bg-clip-text text-transparent;
  }

  .text-golden-gradient {
    @apply bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent;
  }

  /* 神秘按钮效果 */
  .btn-mystical {
    @apply bg-gradient-to-r from-mystical-500 to-mystical-600 text-white shadow-mystical hover:shadow-mystical-lg transition-all duration-300 hover:scale-105;
  }

  .btn-golden {
    @apply bg-gradient-to-r from-gold-500 to-gold-600 text-white shadow-gold hover:shadow-gold transition-all duration-300 hover:scale-105;
  }

  /* 动画效果 */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  /* 星空背景 */
  .star-field {
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
  }

  /* 响应式容器 */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 文本样式 */
  .text-balance {
    text-wrap: balance;
  }

  /* 多语言字体支持 */
  .font-chinese {
    font-family: 'Noto Sans SC', 'PingFang SC', sans-serif;
  }

  .font-japanese {
    font-family: 'Noto Sans JP', sans-serif;
  }

  .font-arabic {
    font-family: 'Noto Sans Arabic', sans-serif;
    direction: rtl;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-mystical-500;
  }

  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02];
  }

  /* 神秘光效 - 根据设计规范更新 */
  .mystical-glow {
    box-shadow:
      0 0 20px rgba(168, 85, 247, 0.3),
      0 0 40px rgba(168, 85, 247, 0.2),
      0 0 60px rgba(168, 85, 247, 0.1);
  }

  .golden-glow {
    box-shadow:
      0 0 20px rgba(245, 158, 11, 0.3),
      0 0 40px rgba(245, 158, 11, 0.2),
      0 0 60px rgba(245, 158, 11, 0.1);
  }

  /* 星座主题光效 */
  .fire-glow {
    box-shadow:
      0 0 20px rgba(255, 107, 107, 0.3),
      0 0 40px rgba(255, 107, 107, 0.2);
  }

  .earth-glow {
    box-shadow:
      0 0 20px rgba(81, 207, 102, 0.3),
      0 0 40px rgba(81, 207, 102, 0.2);
  }

  .air-glow {
    box-shadow:
      0 0 20px rgba(116, 192, 252, 0.3),
      0 0 40px rgba(116, 192, 252, 0.2);
  }

  .water-glow {
    box-shadow:
      0 0 20px rgba(132, 94, 247, 0.3),
      0 0 40px rgba(132, 94, 247, 0.2);
  }
}

@layer utilities {
  /* 隐藏滚动条但保持功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 安全区域适配 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* 多语言字体类 */
  .font-inter {
    font-family: var(--font-inter), 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .font-noto-sans-sc {
    font-family: var(--font-noto-sans-sc), 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;
    line-height: 1.7;
    letter-spacing: 0.05em;
    word-break: break-all;
  }

  .font-noto-sans-jp {
    font-family: var(--font-noto-sans-jp), 'Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', sans-serif;
    line-height: 1.7;
    letter-spacing: 0.05em;
  }

  .font-noto-sans {
    font-family: var(--font-noto-sans), 'Noto Sans Devanagari', 'Mangal', 'Kokila', 'Arial Unicode MS', sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
  }

  .font-noto-sans-arabic {
    font-family: 'Noto Sans Arabic', 'Tahoma', 'Arial Unicode MS', sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
  }

  .font-noto-sans-kr {
    font-family: 'Noto Sans KR', 'Malgun Gothic', 'Apple SD Gothic Neo', sans-serif;
    line-height: 1.6;
    letter-spacing: 0.03em;
  }

  /* RTL语言支持 - 完整版本 */
  .rtl {
    direction: rtl;
    text-align: right;
  }

  /* Flex布局RTL适配 */
  .rtl .flex {
    flex-direction: row-reverse;
  }

  .rtl .flex-row {
    flex-direction: row-reverse;
  }

  .rtl .flex-row-reverse {
    flex-direction: row;
  }

  /* 间距RTL适配 */
  .rtl .space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  /* 边距RTL适配 */
  .rtl .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  .rtl .mr-auto {
    margin-right: 0;
    margin-left: auto;
  }

  .rtl .ml-1 { margin-left: 0; margin-right: 0.25rem; }
  .rtl .ml-2 { margin-left: 0; margin-right: 0.5rem; }
  .rtl .ml-3 { margin-left: 0; margin-right: 0.75rem; }
  .rtl .ml-4 { margin-left: 0; margin-right: 1rem; }
  .rtl .ml-6 { margin-left: 0; margin-right: 1.5rem; }
  .rtl .ml-8 { margin-left: 0; margin-right: 2rem; }

  .rtl .mr-1 { margin-right: 0; margin-left: 0.25rem; }
  .rtl .mr-2 { margin-right: 0; margin-left: 0.5rem; }
  .rtl .mr-3 { margin-right: 0; margin-left: 0.75rem; }
  .rtl .mr-4 { margin-right: 0; margin-left: 1rem; }
  .rtl .mr-6 { margin-right: 0; margin-left: 1.5rem; }
  .rtl .mr-8 { margin-right: 0; margin-left: 2rem; }

  /* 内边距RTL适配 */
  .rtl .pl-1 { padding-left: 0; padding-right: 0.25rem; }
  .rtl .pl-2 { padding-left: 0; padding-right: 0.5rem; }
  .rtl .pl-3 { padding-left: 0; padding-right: 0.75rem; }
  .rtl .pl-4 { padding-left: 0; padding-right: 1rem; }
  .rtl .pl-6 { padding-left: 0; padding-right: 1.5rem; }
  .rtl .pl-8 { padding-left: 0; padding-right: 2rem; }

  .rtl .pr-1 { padding-right: 0; padding-left: 0.25rem; }
  .rtl .pr-2 { padding-right: 0; padding-left: 0.5rem; }
  .rtl .pr-3 { padding-right: 0; padding-left: 0.75rem; }
  .rtl .pr-4 { padding-right: 0; padding-left: 1rem; }
  .rtl .pr-6 { padding-right: 0; padding-left: 1.5rem; }
  .rtl .pr-8 { padding-right: 0; padding-left: 2rem; }

  /* 边框RTL适配 */
  .rtl .border-l { border-left: 0; border-right: 1px solid rgb(var(--border)); }
  .rtl .border-r { border-right: 0; border-left: 1px solid rgb(var(--border)); }
  .rtl .border-l-2 { border-left: 0; border-right: 2px solid rgb(var(--border)); }
  .rtl .border-r-2 { border-right: 0; border-left: 2px solid rgb(var(--border)); }

  /* 圆角RTL适配 */
  .rtl .rounded-l { border-radius: 0 0.25rem 0.25rem 0; }
  .rtl .rounded-r { border-radius: 0.25rem 0 0 0.25rem; }
  .rtl .rounded-l-lg { border-radius: 0 0.5rem 0.5rem 0; }
  .rtl .rounded-r-lg { border-radius: 0.5rem 0 0 0.5rem; }

  /* 定位RTL适配 */
  .rtl .left-0 { left: auto; right: 0; }
  .rtl .right-0 { right: auto; left: 0; }
  .rtl .left-4 { left: auto; right: 1rem; }
  .rtl .right-4 { right: auto; left: 1rem; }

  /* 浮动RTL适配 */
  .rtl .float-left { float: right; }
  .rtl .float-right { float: left; }

  /* 文本对齐RTL适配 */
  .rtl .text-left { text-align: right; }
  .rtl .text-right { text-align: left; }

  /* 变换RTL适配 */
  .rtl .rotate-90 { transform: rotate(-90deg); }
  .rtl .rotate-180 { transform: rotate(180deg); }
  .rtl .-rotate-90 { transform: rotate(90deg); }

  /* 图标RTL适配 */
  .rtl .icon-flip {
    transform: scaleX(-1);
  }

  /* 阴影RTL适配 */
  .rtl .shadow-lg {
    box-shadow: -10px 10px 15px -3px rgba(0, 0, 0, 0.1), -4px 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* 文字长度适配 */
  .text-adaptive {
    width: calc(100% * var(--expansion-factor, 1));
    min-width: fit-content;
  }

  /* 语言特定的排版优化 */
  [lang="zh"] {
    word-break: break-all;
    line-height: 1.7;
  }

  [lang="ja"] {
    word-break: break-all;
    line-height: 1.7;
  }

  [lang="hi"] {
    line-height: 1.8;
  }

  [lang="ar"] {
    direction: rtl;
    text-align: right;
    line-height: 1.8;
  }

  /* 数字和特殊内容在RTL中保持LTR */
  .rtl .ltr-content {
    direction: ltr;
    display: inline-block;
  }

  /* 移动端多语言优化 */
  .mobile-multilingual-wrapper {
    --mobile-touch-target: 44px;
    --mobile-font-size: 16px;
    --mobile-line-height: 1.6;
    --mobile-letter-spacing: 0.01em;
    --mobile-focus-ring: 2px;
    --mobile-animation-duration: 300ms;
  }

  /* 移动端优化类 */
  .mobile-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .mobile-optimized * {
    box-sizing: border-box;
  }

  /* 触摸优化 */
  .touch-optimized {
    min-height: var(--mobile-touch-target);
    min-width: var(--mobile-touch-target);
  }

  .touch-optimized:focus {
    outline: var(--mobile-focus-ring) solid #3b82f6;
    outline-offset: 2px;
  }

  /* 性能优化 */
  .low-end-device * {
    will-change: auto !important;
    transform: none !important;
    transition: none !important;
  }

  .no-animations * {
    animation-duration: 0ms !important;
    animation-delay: 0ms !important;
    transition-duration: 0ms !important;
    transition-delay: 0ms !important;
  }

  /* 高对比度模式 */
  .high-contrast {
    filter: contrast(1.5);
  }

  .high-contrast * {
    border-color: currentColor !important;
  }

  /* 减少动画模式 */
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* 屏幕阅读器优化 */
  .screen-reader-optimized .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .screen-reader-optimized .sr-only:focus {
    position: static;
    width: auto;
    height: auto;
    padding: 0.5rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    border: 2px solid #3b82f6;
    background: white;
    color: black;
    z-index: 9999;
  }

  /* 移动端文本优化 */
  .mobile-optimized-text {
    font-size: var(--mobile-font-size);
    line-height: var(--mobile-line-height);
    letter-spacing: var(--mobile-letter-spacing);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 移动端按钮优化 */
  .mobile-optimized-button {
    min-height: var(--mobile-touch-target);
    transition-duration: var(--mobile-animation-duration);
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-optimized-button:focus {
    outline: var(--mobile-focus-ring) solid #3b82f6;
    outline-offset: 2px;
  }

  .mobile-optimized-button:active {
    transform: scale(0.98);
  }

  .no-animations .mobile-optimized-button:active {
    transform: none;
  }

  /* 移动端输入框优化 */
  .mobile-optimized-input {
    min-height: var(--mobile-touch-target);
    font-size: var(--mobile-font-size);
  }

  .mobile-input {
    /* 防止iOS Safari缩放 */
    font-size: 16px !important;
  }

  .mobile-optimized-input:focus {
    outline: var(--mobile-focus-ring) solid #3b82f6;
    outline-offset: 2px;
  }

  /* 移动端卡片优化 */
  .mobile-optimized-card {
    transition-duration: var(--mobile-animation-duration);
  }

  .mobile-optimized-card.touch-optimized {
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }

  .mobile-optimized-card:active {
    transform: scale(0.99);
  }

  .no-animations .mobile-optimized-card:active {
    transform: none;
  }

  /* 移动端特定的媒体查询 */
  @media (max-width: 768px) {
    .mobile-multilingual-wrapper {
      --mobile-touch-target: 48px;
      --mobile-font-size: 17px;
      --mobile-line-height: 1.7;
      --mobile-focus-ring: 3px;
    }

    /* 移动端间距调整 */
    .mobile-optimized .space-x-2 > :not([hidden]) ~ :not([hidden]) {
      margin-left: 0.75rem;
      margin-right: 0;
    }

    .mobile-optimized .space-y-2 > :not([hidden]) ~ :not([hidden]) {
      margin-top: 0.75rem;
      margin-bottom: 0;
    }

    /* 移动端字体大小调整 */
    .mobile-optimized h1 { font-size: 1.875rem; }
    .mobile-optimized h2 { font-size: 1.5rem; }
    .mobile-optimized h3 { font-size: 1.25rem; }
    .mobile-optimized h4 { font-size: 1.125rem; }
    .mobile-optimized h5 { font-size: 1rem; }
    .mobile-optimized h6 { font-size: 0.875rem; }
  }

  /* 超小屏幕优化 */
  @media (max-width: 480px) {
    .mobile-multilingual-wrapper {
      --mobile-touch-target: 52px;
      --mobile-font-size: 18px;
      --mobile-line-height: 1.8;
    }

    .mobile-optimized {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }

  /* 横屏模式优化 */
  @media (max-height: 500px) and (orientation: landscape) {
    .mobile-multilingual-wrapper {
      --mobile-touch-target: 40px;
    }

    .mobile-optimized {
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;
    }
  }

  /* 高DPI屏幕优化 */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-optimized-text {
      -webkit-font-smoothing: subpixel-antialiased;
    }
  }
}
