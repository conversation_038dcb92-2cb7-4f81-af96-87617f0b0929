'use client';

import { useCultural } from '@/components/ui/cultural-provider';
import { cn } from '@/lib/utils';

// 符号类型
export type SymbolType = 'success' | 'error' | 'warning' | 'info' | 'luck' | 'spirituality';

// 符号组件属性
interface CulturalSymbolProps {
  type: SymbolType;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallback?: string;
}

// 文化敏感的符号组件
export function CulturalSymbol({ 
  type, 
  size = 'md', 
  className,
  fallback 
}: CulturalSymbolProps) {
  const { config } = useCultural();
  
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };
  
  const symbol = config.symbols[type] || fallback || '•';
  
  return (
    <span 
      className={cn(
        'inline-block',
        sizeClasses[size],
        className
      )}
      role="img"
      aria-label={`${type} symbol`}
    >
      {symbol}
    </span>
  );
}

// 状态指示器组件
interface CulturalStatusIndicatorProps {
  status: 'success' | 'error' | 'warning' | 'info';
  message: string;
  showIcon?: boolean;
  className?: string;
}

export function CulturalStatusIndicator({
  status,
  message,
  showIcon = true,
  className,
}: CulturalStatusIndicatorProps) {
  const { config } = useCultural();
  
  const statusColors = {
    success: 'text-green-600 bg-green-50 border-green-200',
    error: 'text-red-600 bg-red-50 border-red-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    info: 'text-blue-600 bg-blue-50 border-blue-200',
  };
  
  return (
    <div 
      className={cn(
        'flex items-center gap-2 p-3 rounded-md border',
        statusColors[status],
        className
      )}
    >
      {showIcon && (
        <CulturalSymbol 
          type={status} 
          size="md"
          className="flex-shrink-0"
        />
      )}
      <span className="text-sm font-medium">{message}</span>
    </div>
  );
}

// 幸运符号组件
interface LuckySymbolProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
}

export function LuckySymbol({ 
  className, 
  size = 'md', 
  animated = false 
}: LuckySymbolProps) {
  const { config } = useCultural();
  
  return (
    <CulturalSymbol
      type="luck"
      size={size}
      className={cn(
        'text-yellow-500',
        animated && 'animate-pulse',
        className
      )}
    />
  );
}

// 精神符号组件
interface SpiritualSymbolProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  glowing?: boolean;
}

export function SpiritualSymbol({ 
  className, 
  size = 'md', 
  glowing = false 
}: SpiritualSymbolProps) {
  const { config } = useCultural();
  
  return (
    <CulturalSymbol
      type="spirituality"
      size={size}
      className={cn(
        'text-mystical-500',
        glowing && 'animate-mystical-glow',
        className
      )}
    />
  );
}

// 文化敏感的颜色组件
interface CulturalColorProps {
  type: 'primary' | 'secondary' | 'accent' | 'lucky' | 'unlucky';
  children: React.ReactNode;
  className?: string;
  variant?: 'text' | 'bg' | 'border';
}

export function CulturalColor({ 
  type, 
  children, 
  className,
  variant = 'text' 
}: CulturalColorProps) {
  const { config } = useCultural();
  
  const getColorClass = () => {
    const colorName = config.colorPreferences[type];
    switch (variant) {
      case 'bg':
        return `bg-${colorName}`;
      case 'border':
        return `border-${colorName}`;
      case 'text':
      default:
        return `text-${colorName}`;
    }
  };
  
  return (
    <span className={cn(getColorClass(), className)}>
      {children}
    </span>
  );
}

// 文化敏感的数字格式化组件
interface CulturalNumberProps {
  value: number;
  type?: 'decimal' | 'currency' | 'percent';
  className?: string;
}

export function CulturalNumber({ 
  value, 
  type = 'decimal', 
  className 
}: CulturalNumberProps) {
  const { config } = useCultural();
  
  const formatNumber = () => {
    const formatter = new Intl.NumberFormat(config.numberFormat, {
      style: type,
      currency: type === 'currency' ? config.currency : undefined,
    });
    
    return formatter.format(value);
  };
  
  return (
    <span className={cn('tabular-nums', className)}>
      {formatNumber()}
    </span>
  );
}

// 文化敏感的日期格式化组件
interface CulturalDateProps {
  date: Date;
  format?: 'short' | 'long' | 'full';
  className?: string;
}

export function CulturalDate({ 
  date, 
  format = 'short', 
  className 
}: CulturalDateProps) {
  const { config, locale } = useCultural();
  
  const formatDate = () => {
    const options: Intl.DateTimeFormatOptions = {
      short: { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      },
      long: { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      },
      full: { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long',
        hour: 'numeric',
        minute: 'numeric'
      },
    }[format];
    
    const formatter = new Intl.DateTimeFormat(config.numberFormat, options);
    return formatter.format(date);
  };
  
  return (
    <time 
      dateTime={date.toISOString()}
      className={className}
    >
      {formatDate()}
    </time>
  );
}

// 文化敏感的文本长度适配组件
interface CulturalTextProps {
  children: React.ReactNode;
  adaptive?: boolean;
  className?: string;
}

export function CulturalText({ 
  children, 
  adaptive = true, 
  className 
}: CulturalTextProps) {
  const { config } = useCultural();
  
  const adaptiveStyles = adaptive ? {
    '--expansion-factor': config.expansionFactor,
    lineHeight: config.lineHeight,
    letterSpacing: config.letterSpacing,
    wordBreak: config.wordBreak as any,
  } : {};
  
  return (
    <span 
      className={cn(
        adaptive && 'text-adaptive',
        className
      )}
      style={adaptiveStyles}
    >
      {children}
    </span>
  );
}

// 文化敏感的按钮文本组件
interface CulturalButtonTextProps {
  children: React.ReactNode;
  className?: string;
}

export function CulturalButtonText({ 
  children, 
  className 
}: CulturalButtonTextProps) {
  const { config } = useCultural();
  
  return (
    <CulturalText 
      adaptive={true}
      className={cn(
        'inline-block min-w-fit',
        // 根据扩展因子调整最小宽度
        config.expansionFactor > 1.2 && 'min-w-[120%]',
        className
      )}
    >
      {children}
    </CulturalText>
  );
}

// 文化敏感的卡片标题组件
interface CulturalCardTitleProps {
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

export function CulturalCardTitle({ 
  children, 
  level = 2, 
  className 
}: CulturalCardTitleProps) {
  const { config } = useCultural();
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  
  return (
    <Tag 
      className={cn(
        'font-semibold',
        // 根据语言调整标题样式
        config.fontFamily === 'chinese' && 'font-medium',
        config.fontFamily === 'japanese' && 'font-medium',
        config.fontFamily === 'hindi' && 'font-semibold',
        className
      )}
      style={{
        lineHeight: config.lineHeight,
        letterSpacing: config.letterSpacing,
      }}
    >
      <CulturalText adaptive={true}>
        {children}
      </CulturalText>
    </Tag>
  );
}

// 导出所有组件
export {
  CulturalSymbol,
  CulturalStatusIndicator,
  LuckySymbol,
  SpiritualSymbol,
  CulturalColor,
  CulturalNumber,
  CulturalDate,
  CulturalText,
  CulturalButtonText,
  CulturalCardTitle,
};
